[ 2025-08-09T12:39:45+08:00 ][ error ] 收到消息：dz/pi/setack/710003
error
[ 2025-08-09T12:39:45+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710003',
  'message' => '{"VER":"0","CMD":"17","CD":"710003","CL":"0","PACKID":16967,"STATUS":"1"}',
  'timestamp' => '2025-08-09 12:39:45',
)
error
[ 2025-08-09T12:39:45+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T12:39:45+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710003',
  'CL' => '0',
  'PACKID' => 16967,
  'STATUS' => '1',
)
error
[ 2025-08-09T12:39:45+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710003
error
[ 2025-08-09T12:39:45+08:00 ][ log ] 【命令响应】收到针对命令包 '16967' 的ACK确认
log
[ 2025-08-09T12:39:45+08:00 ][ log ] 【命令响应】成功更新命令 '16967' 的状态为 'acked'
log
[ 2025-08-09T12:40:54+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-09T12:40:54+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"1","SS":"0","BS":"11.6","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-09 12:40:54',
)
error
[ 2025-08-09T12:40:54+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T12:40:54+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '11.6',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-09T12:40:54+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710003
error
[ 2025-08-09T12:40:54+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T12:40:54+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-09T12:40:54+08:00 ][ log ] 【状态上报】成功更新地锁 '710003' 的快照状态到数据库
log
[ 2025-08-09T12:40:54+08:00 ][ log ] 【状态上报】成功记录地锁 '710003' 的状态到日志表
log
[ 2025-08-09T12:40:54+08:00 ][ log ] 【业务触发】检查 '710003' 的状态变化...
log
[ 2025-08-09T12:40:54+08:00 ][ log ] 【业务触发】检测到地锁 '710003' 关锁成功，开始处理订单...
log
[ 2025-08-09T12:40:54+08:00 ][ error ] 收到消息：dz/pi/setack/710003
error
[ 2025-08-09T12:40:54+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710003',
  'message' => '{"VER":"0","CMD":"17","CD":"710003","CL":"1","PACKID":17213,"STATUS":"81"}',
  'timestamp' => '2025-08-09 12:40:54',
)
error
[ 2025-08-09T12:40:54+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T12:40:54+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710003',
  'CL' => '1',
  'PACKID' => 17213,
  'STATUS' => '81',
)
error
[ 2025-08-09T12:40:54+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710003
error
[ 2025-08-09T12:40:54+08:00 ][ log ] 【命令响应】收到针对命令包 '17213' 的ACK确认
log
[ 2025-08-09T12:40:54+08:00 ][ log ] 【命令响应】成功更新命令 '17213' 的状态为 'acked'
log
[ 2025-08-09T12:41:07+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-09T12:41:07+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"0","SS":"0","BS":"11.7","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-09 12:41:07',
)
error
[ 2025-08-09T12:41:07+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T12:41:07+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.7',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-09T12:41:07+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710003
error
[ 2025-08-09T12:41:07+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T12:41:07+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-09T12:41:07+08:00 ][ log ] 【状态上报】成功更新地锁 '710003' 的快照状态到数据库
log
[ 2025-08-09T12:41:07+08:00 ][ log ] 【状态上报】成功记录地锁 '710003' 的状态到日志表
log
[ 2025-08-09T12:41:07+08:00 ][ log ] 【业务触发】检查 '710003' 的状态变化...
log
[ 2025-08-09T12:41:07+08:00 ][ log ] 【业务触发】检测到地锁 '710003' 开锁成功，结束处理订单...
log
[ 2025-08-09T12:41:07+08:00 ][ log ] 打印order
log
[ 2025-08-09T12:41:07+08:00 ][ log ] array (
  'id' => 42,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '0.00',
  'hospital_hourlong' => 0,
  'hospital_freedt' => 0,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710003-710003',
  'sn' => 'ord2025080912404911215208',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754714454,
  'returntime' => NULL,
  'updatetime' => 1754714449,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754751600,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-09T12:41:07+08:00 ][ log ] 开始实例化ord
log
[ 2025-08-09T12:41:07+08:00 ][ log ] 初始化开始
log
[ 2025-08-09T12:41:07+08:00 ][ error ] 消息处理失败: Undefined index: HTTP_HOST
error
[ 2025-08-09T12:41:08+08:00 ][ error ] 收到消息：dz/pi/setack/710003
error
[ 2025-08-09T12:41:08+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710003',
  'message' => '{"VER":"0","CMD":"17","CD":"710003","CL":"0","PACKID":46427,"STATUS":"1"}',
  'timestamp' => '2025-08-09 12:41:08',
)
error
[ 2025-08-09T12:41:08+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T12:41:08+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710003',
  'CL' => '0',
  'PACKID' => 46427,
  'STATUS' => '1',
)
error
[ 2025-08-09T12:41:08+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710003
error
[ 2025-08-09T12:41:08+08:00 ][ log ] 【命令响应】收到针对命令包 '46427' 的ACK确认
log
[ 2025-08-09T12:41:08+08:00 ][ log ] 【命令响应】成功更新命令 '46427' 的状态为 'acked'
log
[ 2025-08-09T12:51:08+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-09T12:51:08+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 12:51:08',
)
error
[ 2025-08-09T12:51:08+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T12:51:08+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T12:51:08+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710003
error
[ 2025-08-09T12:51:08+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T12:51:08+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-09T12:51:08+08:00 ][ log ] 【状态上报】成功更新地锁 '710003' 的快照状态到数据库
log
[ 2025-08-09T12:51:08+08:00 ][ log ] 【状态上报】成功记录地锁 '710003' 的状态到日志表
log
[ 2025-08-09T12:51:08+08:00 ][ log ] 【业务触发】检查 '710003' 的状态变化...
log
[ 2025-08-09T13:01:08+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-09T13:01:08+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 13:01:08',
)
error
[ 2025-08-09T13:01:08+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T13:01:08+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T13:01:08+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710003
error
[ 2025-08-09T13:01:08+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T13:01:08+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-09T13:01:08+08:00 ][ log ] 【状态上报】成功更新地锁 '710003' 的快照状态到数据库
log
[ 2025-08-09T13:01:08+08:00 ][ log ] 【状态上报】成功记录地锁 '710003' 的状态到日志表
log
[ 2025-08-09T13:01:08+08:00 ][ log ] 【业务触发】检查 '710003' 的状态变化...
log
[ 2025-08-09T13:18:03+08:00 ][ error ] 收到消息：dz/pi/mstatus/710003
error
[ 2025-08-09T13:18:03+08:00 ][ error ] array (
  'topic' => 'dz/pi/mstatus/710003',
  'message' => '{"MD":"710003","MS":"0","SS":"0"}',
  'timestamp' => '2025-08-09 13:18:03',
)
error
[ 2025-08-09T13:18:03+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T13:18:03+08:00 ][ error ] array (
  'MD' => '710003',
  'MS' => '0',
  'SS' => '0',
)
error
[ 2025-08-09T13:18:03+08:00 ][ log ] 收到地锁 '710003' 的状态更新: 离线, 信号强度: 0
log
[ 2025-08-09T13:18:03+08:00 ][ log ] 成功更新地锁 '710003' 的数据库状态为: 离线
log
[ 2025-08-09T15:23:07+08:00 ][ error ] 收到消息：dz/pi/mstatus/710001
error
[ 2025-08-09T15:23:07+08:00 ][ error ] array (
  'topic' => 'dz/pi/mstatus/710001',
  'message' => '{"MD":"710001","MS":"1","SS":"23"}',
  'timestamp' => '2025-08-09 15:23:07',
)
error
[ 2025-08-09T15:23:07+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T15:23:07+08:00 ][ error ] array (
  'MD' => '710001',
  'MS' => '1',
  'SS' => '23',
)
error
[ 2025-08-09T15:23:07+08:00 ][ log ] 收到地锁 '710001' 的状态更新: 上线, 信号强度: 23
log
[ 2025-08-09T15:23:07+08:00 ][ log ] 未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T15:23:08+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T15:23:08+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.0","RSSI":23,"MT":"5","NG":"0"}',
  'timestamp' => '2025-08-09 15:23:08',
)
error
[ 2025-08-09T15:23:08+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T15:23:08+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.0',
  'RSSI' => 23,
  'MT' => '5',
  'NG' => '0',
)
error
[ 2025-08-09T15:23:08+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T15:23:08+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T15:23:08+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T15:23:08+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T15:33:07+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T15:33:07+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 15:33:07',
)
error
[ 2025-08-09T15:33:07+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T15:33:07+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T15:33:07+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T15:33:07+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T15:33:07+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T15:33:07+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T15:43:05+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T15:43:05+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 15:43:05',
)
error
[ 2025-08-09T15:43:05+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T15:43:05+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T15:43:05+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T15:43:05+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T15:43:05+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T15:43:05+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T15:53:04+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T15:53:04+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 15:53:04',
)
error
[ 2025-08-09T15:53:04+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T15:53:04+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T15:53:04+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T15:53:04+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T15:53:04+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T15:53:04+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T16:03:03+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T16:03:03+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 16:03:03',
)
error
[ 2025-08-09T16:03:03+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T16:03:03+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T16:03:03+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T16:03:03+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T16:03:03+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T16:03:03+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T16:13:02+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T16:13:02+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 16:13:02',
)
error
[ 2025-08-09T16:13:02+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T16:13:02+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T16:13:02+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T16:13:02+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T16:13:02+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T16:13:02+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T16:23:01+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T16:23:01+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 16:23:01',
)
error
[ 2025-08-09T16:23:01+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T16:23:01+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T16:23:01+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T16:23:01+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T16:23:01+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T16:23:01+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T16:33:00+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T16:33:00+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 16:33:00',
)
error
[ 2025-08-09T16:33:00+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T16:33:00+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T16:33:00+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T16:33:00+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T16:33:00+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T16:33:00+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T16:42:59+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T16:42:59+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 16:42:59',
)
error
[ 2025-08-09T16:42:59+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T16:42:59+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T16:42:59+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T16:42:59+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T16:42:59+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T16:42:59+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T16:52:58+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T16:52:58+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 16:52:58',
)
error
[ 2025-08-09T16:52:58+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T16:52:58+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T16:52:58+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T16:52:58+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T16:52:58+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T16:52:58+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T17:02:57+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T17:02:57+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 17:02:57',
)
error
[ 2025-08-09T17:02:57+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T17:02:57+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T17:02:57+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T17:02:57+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T17:02:57+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T17:02:57+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T17:12:56+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T17:12:56+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 17:12:56',
)
error
[ 2025-08-09T17:12:56+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T17:12:56+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T17:12:56+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T17:12:56+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T17:12:56+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T17:12:56+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T17:22:55+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T17:22:55+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 17:22:55',
)
error
[ 2025-08-09T17:22:55+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T17:22:55+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T17:22:55+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T17:22:55+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T17:22:55+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T17:22:55+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T17:32:54+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T17:32:54+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 17:32:54',
)
error
[ 2025-08-09T17:32:54+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T17:32:54+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T17:32:54+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T17:32:54+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T17:32:54+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T17:32:54+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T17:42:53+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T17:42:53+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 17:42:53',
)
error
[ 2025-08-09T17:42:53+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T17:42:53+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T17:42:53+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T17:42:53+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T17:42:53+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T17:42:53+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T17:52:52+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T17:52:52+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 17:52:52',
)
error
[ 2025-08-09T17:52:52+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T17:52:52+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T17:52:52+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T17:52:52+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T17:52:52+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T17:52:52+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T18:02:51+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-09T18:02:51+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-09 18:02:51',
)
error
[ 2025-08-09T18:02:51+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T18:02:51+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-09T18:02:51+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-09T18:02:51+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-09T18:02:51+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-09T18:02:51+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710001' 的地锁设备
log
[ 2025-08-09T18:19:34+08:00 ][ error ] 收到消息：dz/pi/mstatus/710001
error
[ 2025-08-09T18:19:34+08:00 ][ error ] array (
  'topic' => 'dz/pi/mstatus/710001',
  'message' => '{"MD":"710001","MS":"0","SS":"0"}',
  'timestamp' => '2025-08-09 18:19:34',
)
error
[ 2025-08-09T18:19:34+08:00 ][ error ] 解析数据：
error
[ 2025-08-09T18:19:34+08:00 ][ error ] array (
  'MD' => '710001',
  'MS' => '0',
  'SS' => '0',
)
error
[ 2025-08-09T18:19:34+08:00 ][ log ] 收到地锁 '710001' 的状态更新: 离线, 信号强度: 0
log
[ 2025-08-09T18:19:34+08:00 ][ log ] 未在数据库中找到SN为 '710001' 的地锁设备
log

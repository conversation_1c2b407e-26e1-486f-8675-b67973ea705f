
const app = getApp()


/**
 *  调用接口
 */
export function request(data, fun) {
  wx.request({
    method: 'POST', // 请求方式
    url: 'https://shuziyaoshi.zgxiaochengxu.com/api/index/aesjjm',
    header: { 'Content-Type': 'application/json' },
    data: data,
    // header : {'Content-Type': 'multipart/form-data'},
    // header : {'Content-Type':'application/x-www-form-urlencoded'},
    success: function (res) {
        fun(res);
    }
});
}

/**
 * 判断用户小程序版本版本
 * @param {*} ver1 
 * @param {*} ver2 
 */
export function versionCompare(ver1, ver2) {
  var version1pre = parseFloat(ver1);
  var version2pre = parseFloat(ver2);
  var version1next = parseInt(ver1.replace(version1pre + ".", ""));
  var version2next = parseInt(ver2.replace(version2pre + ".", ""));
  if (version1pre > version2pre) {
    return true;
  } else if (version1pre < version2pre) {
    return false;
  } else {
    if (version1next > version2next) {
      return true;
    } else {
      return false;
    }
  }
}

// pages/index/index.js
const app = getApp()
var QQMapWX = require('../../utils/qqmap-wx-jssdk.js');
var qqmapsdk;
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		statusBar: app.globalData.statusBar,
		customBar: app.globalData.customBar,
		custom: app.globalData.custom,


		province: '',
		city: '',
		latitude: '',
		longitude: '',

		type: '', // 扫码状态 1租借 2归还
		kpl: '', // 小程序扫码设备码
		sid: '', // 微信扫码设备码
		mobileShow: false,
		userinfo:{},
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		wx.hideTabBar();
		let that = this
		qqmapsdk = new QQMapWX({
			key: 'DJBBZ-PFEAQ-6QP5R-2NPHQ-SZ3QQ-QMBIP' //这里自己的key秘钥进行填充
		});
		const {
			q
		} = options;
		if (q) {
			let urlStr = decodeURIComponent(q);
			console.log(urlStr)
			let id = urlStr.substring(36, urlStr.length);
			that.setData({
				sid: id,
				type: '',
			})
			if (!wx.getStorageSync("token")) {
				app.toLogin().then(res => {
					if (wx.getStorageSync("SDKVersion") >= '2.21.2') {
						// wx.redirectTo({
						// 	url: 'login?type=1'
						// })
					} else {
						if (that.data.userinfo.mobile) {
							that.getOrderinfor()
						} else {
							that.setData({
								mobileShow: true,
							});
						}
					}
				}).catch(() => {

				});
			} else {
				that.getOrderinfor()
			}
		}
		that.getBanner()
	},

	handleA(){
		this.setData({
			mobileShow:false,
		})
	},

	getInfo(){
		app.get('user/profile', {}).then(res => {
			const { code, data,msg} = res //接口数据
			console.log('getInfo',res);
      if (code == 1) {
        this.setData({
					userinfo:data,
				})
      }
    }).catch((err) => {

    })
	},

	// 获取广告
	getBanner() {
		let that = this
		let params = {
			types: 'index'
		}
		app.post('index/ad', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					banner: data,
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	//获取当前城市
	getUserLocation() {
		var that = this;
		wx.getSetting({
			success: (res) => {
				// console.log(JSON.stringify(res))
				// res.authSetting['scope.userLocation'] == undefined  表示 初始化进入该页面
				// res.authSetting['scope.userLocation'] == false  表示 非初始化进入该页面,且未授权
				// res.authSetting['scope.userLocation'] == true  表示 地理位置授权
				if (res.authSetting['scope.userLocation'] != undefined && res.authSetting['scope.userLocation'] != true) {
					wx.showModal({
						title: '请求授权当前位置',
						content: '需要获取您的地理位置，请确认授权',
						success: function (res) {
							if (res.cancel) {
								wx.showToast({
									title: '拒绝授权',
									icon: 'none',
									duration: 1000
								})
							} else if (res.confirm) {
								wx.openSetting({
									success: function (dataAu) {
										if (dataAu.authSetting["scope.userLocation"] == true) {
											wx.showToast({
												title: '授权成功',
												icon: 'success',
												duration: 1000
											})
											//再次授权，调用wx.getLocation的API
											that.getLocation();
										} else {
											wx.showToast({
												title: '授权失败',
												icon: 'none',
												duration: 1000
											})
										}
									}
								})
							}
						}
					})
				} else if (res.authSetting['scope.userLocation'] == undefined) {
					//调用wx.getLocation的API
					that.getLocation();
				} else {
					//调用wx.getLocation的API
					that.getLocation();
				}
			}
		})
	},

	// 微信获得经纬度
	getLocation() {
		var that = this;
		wx.getLocation({
			type: 'wgs84',
			success: function (res) {
				console.log(JSON.stringify(res))
				var latitude = res.latitude
				var longitude = res.longitude
				that.setData({
					latitude: res.latitude,
					longitude: res.longitude
				})
				wx.setStorageSync('lat', res.latitude);
				wx.setStorageSync('lon', res.longitude);
				that.getLocal(latitude, longitude)
			},
			fail: function (res) {
				console.log('fail' + JSON.stringify(res))
				wx.showModal({
					title: '提示',
					content: '请开启手机位置权限！',
					showCancel: false,
					success(res) {

					}
				})
			}
		})
	},

	// 获取当前地理位置
	getLocal(latitude, longitude) {
		var that = this;
		qqmapsdk.reverseGeocoder({
			location: {
				latitude: latitude,
				longitude: longitude
			},
			success: function (res) {
				console.log(res.result.ad_info);
				let province = res.result.ad_info.province
				let city = res.result.ad_info.city
				app.globalData.city = city;
				that.setData({
					province: province,
					city: city,
					latitude: latitude,
					longitude: longitude
				})
			},
			fail: function (res) {
				console.log(res);
			},
			complete: function (res) {
				// console.log(res);
			}
		});
	},

	// 附近门店
	goStore() {
		if (!this.data.latitude) {
			wx.showModal({
				title: '提示',
				content: '请开启手机位置权限！',
				showCancel: false,
				success(res) { }
			})
			return
		}
		wx.navigateTo({
			// url: '/packageA/lockset/lockset',
			url: '/packageA/stores/list',
		})
	},

	// 手机号绑定（使用公共方法）
	getPhoneNumber(e) {
		let that = this;

		// 使用app.js中的公共方法获取手机号
		app.getWechatPhoneNumber(e,
			// 成功回调
			function(mobile) {
				// 隐藏手机号输入弹窗
				that.setData({
					mobileShow: false,
				});

				// 根据当前状态决定下一步操作
				if (that.data.sid) {
					// 如果已有设备ID，获取订单信息
					that.getOrderinfor()
				} else {
					// 否则进行扫码
					that.getScanCode()
				}
			},
			// 失败回调
			function(errorMsg) {
				console.log('获取手机号失败：', errorMsg);
				// 错误提示已在公共方法中处理，这里可以添加额外的业务逻辑
			}
		);
	},

	// 扫码租借
	getCode(e) {
		let that = this
		that.setData({
			sid: '',
			type: e.currentTarget.dataset.type,
		})
		if (!wx.getStorageSync('lat')) {
			wx.showModal({
				title: '提示',
				content: '请开启手机位置权限！',
				showCancel: false,
				success(res) { }
			})
			return
		}
		// 去掉蓝牙检测限制，直接执行扫码逻辑
		if (!wx.getStorageSync("token")) {
			app.toLogin().then(res => {
				if (wx.getStorageSync("SDKVersion") >= '2.21.2') {
					// wx.redirectTo({
					// 	url: 'login?type=1'
					// })
				} else {
					if (that.data.userinfo.mobile) {
						that.getScanCode()
					} else {
						that.setData({
							mobileShow: true,
						});
					}
				}
			}).catch(() => {

			});
		} else {
			if (that.data.userinfo.mobile) {
				that.getScanCode()
			} else {
				that.setData({
					mobileShow: true,
				});
			}
		}
	},

	// 扫码
	getScanCode() {
		let that = this
		// type 1扫码租借 2车位归还
		let type = that.data.type
		console.log(type)
		console.log(that.data.latitude)
		wx.scanCode({
			success(res) {
				let path = res.result;
				console.log('path',path);
				let id = path.split('/kpl/')[1];
				// let id = path.substring(36, path.length);
				that.setData({
					kpl: id
				})
				if (type == 1) {
					// 租借
					that.getDefault()
				} else {
					// 归还
					that.getOrderinfor()
				}
			}
		})
	},

	// 1-验证用户是否符合租用条件
	getDefault() {
		let params = {}
		app.post('User/getDefault', params).then(res => {
			const {code,	data,msg} = res //接口数据
			if (code == 1) {
				if (data.code == 1) {
					this.getEquipmentInfo()
				} else if (data.code == 202) {
					if (data.order.status == 1) {
						wx.showModal({
							title: '提示',
							content: '您有未归还的设备，归还后才可以租借',
							showCancel: false,
							success: function (res) {
								if (res.confirm) { }
							}
						})
					} else if (data.order.status == 2) {
						wx.showModal({
							title: '提示',
							content: '您有未支付的设备，支付后可以重新租借',
							showCancel: false,
							success: function (res) {
								if (res.confirm) {
									// 订单详情
									wx.navigateTo({
										url: '/packageB/order/index?type=2',
									})
								}
							}
						})
					}
				}
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 3000
				})
			}
		}).catch((err) => {

		})
	},
	// 2-租借-用户扫码后验证设备是否可用
	getEquipmentInfo() {
		let that = this
		let params = {
			kpl: that.data.sid ? that.data.sid : that.data.kpl
		}
		app.post('Ajax/getEquipmentInfo', params).then(res => {
			const {code,data,msg} = res //接口数据
			if (code == 1) {
				wx.navigateTo({
					url: '/packageA/lease/lease?id=' + data.id,
					// url: '/packageA/rental/index?type=1' + '&id=' + data.id,
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 3000
				})
			}
		}).catch((err) => {

		})
	},


	// 5-结束订单-查询订单信息
	getOrderinfor() {
		let that = this
		let type = that.data.type
		let sid = that.data.sid
		console.log(that.data.type)
		console.log(that.data.kpl)
		let params = {
			id: sid ? that.data.sid : that.data.kpl
		}
		app.post('Order/orderInfo', params).then(res => {
			const {	code,	data,	msg} = res //接口数据
			if (code == 0) {
				// 	没有这个设备的租用信息 租借设备
				if (sid) {
					that.getDefault()
				} else {
					wx.showToast({
						title: '无租借订单，请先租借！',
						icon: 'none',
						duration: 3000
					})
				}
			} else {
				// 有这个设备的租用信息 归还
				// let url='/packageA/rental/index?id=' + data.device.id + '&ordercode=' + data.order.ordercode
				let url='/packageA/lockset/lockset?id=' + data.device.id + '&ordercode=' + data.order.ordercode
				if (data.order.status == 1) {
					if (sid) {
						wx.navigateTo({
							url: url,
						})
					} else {
						if (type == 1) {
							wx.showModal({
								title: '提示',
								content: '您有未归还的设备，归还后才可以租借',
								showCancel: false,
								success: function (res) {
									if (res.confirm) { }
								}
							})
						} else {
							wx.navigateTo({
								url: url,
							})
						}
					}
				} else if (data.order.status == 2) {
					wx.showModal({
						title: '提示',
						content: '您有未支付的设备，请先支付',
						showCancel: false,
						success: function (res) {
							if (res.confirm) {
								// 订单列表
								wx.navigateTo({
									url: '/packageB/order/index?type=2',
								})
							}
						}
					})
				}
			}
		}).catch((err) => {

		})
	},

	// 购买套餐
	goPackage() {
		if (!this.data.latitude) {
			wx.showModal({
				title: '提示',
				content: '请开启手机位置权限！',
				showCancel: false,
				success(res) { }
			})
			return
		}
		if (!wx.getStorageSync("token")) {
			app.toLogin().then(res => {
				if (wx.getStorageSync("SDKVersion") >= '2.21.2') {
					// wx.redirectTo({
					// 	url: 'login?type=2'
					// })
				} else {
					wx.navigateTo({
						url: '/packageA/package/index',
					})
				}
			}).catch(() => {

			});
		} else {
			wx.navigateTo({
				url: '/packageA/package/index',
			})
		}
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {
		this.getUserLocation(); //获取当前城市
		// 去掉蓝牙检测限制，只保留位置权限获取
		this.getInfo();
	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		this.setData({
			type: '', // 扫码状态 1租借 2归还
			kpl: '', // 小程序扫码设备码
			sid: '', // 微信扫码设备码
		})
	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},
	/**
 * 用户点击右上角分享
 */
	onShareAppMessage: function () {

	},
})
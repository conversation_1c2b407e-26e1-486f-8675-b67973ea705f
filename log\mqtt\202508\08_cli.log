[ 2025-08-08T17:44:17+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-08T17:44:17+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"1","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-08 17:44:17',
)
error
[ 2025-08-08T17:44:17+08:00 ][ error ] 解析数据：
error
[ 2025-08-08T17:44:17+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-08T17:44:17+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-08T17:44:17+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-08T17:44:17+08:00 ][ log ] 【状态上报】成功更新地锁 '710003' 的快照状态到数据库
log
[ 2025-08-08T17:44:17+08:00 ][ log ] 【状态上报】成功记录地锁 '710003' 的状态到日志表
log
[ 2025-08-08T17:44:17+08:00 ][ log ] 【业务触发】检查 '710003' 的状态变化...
log
[ 2025-08-08T17:52:31+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-08T17:52:31+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"1","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-08 17:52:31',
)
error
[ 2025-08-08T17:52:31+08:00 ][ error ] 解析数据：
error
[ 2025-08-08T17:52:31+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-08T17:52:31+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-08T17:52:31+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-08T17:52:31+08:00 ][ log ] 【状态上报】成功更新地锁 '710003' 的快照状态到数据库
log
[ 2025-08-08T17:52:31+08:00 ][ log ] 【状态上报】成功记录地锁 '710003' 的状态到日志表
log
[ 2025-08-08T17:52:31+08:00 ][ log ] 【业务触发】检查 '710003' 的状态变化...
log
[ 2025-08-08T17:58:40+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-08T17:58:40+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"0","SS":"0","BS":"11.7","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-08 17:58:40',
)
error
[ 2025-08-08T17:58:40+08:00 ][ error ] 解析数据：
error
[ 2025-08-08T17:58:40+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.7',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-08T17:58:40+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-08T17:58:40+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-08T17:58:40+08:00 ][ log ] 【状态上报】成功更新地锁 '710003' 的快照状态到数据库
log
[ 2025-08-08T17:58:40+08:00 ][ log ] 【状态上报】成功记录地锁 '710003' 的状态到日志表
log
[ 2025-08-08T17:58:40+08:00 ][ log ] 【业务触发】检查 '710003' 的状态变化...
log
[ 2025-08-08T17:58:40+08:00 ][ log ] 【业务触发】检测到地锁 '710003' 开锁成功，结束处理订单...
log
[ 2025-08-08T17:58:40+08:00 ][ error ] 收到消息：dz/pi/setack/710003
error
[ 2025-08-08T17:58:40+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710003',
  'message' => '{"VER":"0","CMD":"17","CD":"710003","CL":"0","PACKID":28282,"STATUS":"1"}',
  'timestamp' => '2025-08-08 17:58:40',
)
error
[ 2025-08-08T17:58:40+08:00 ][ error ] 解析数据：
error
[ 2025-08-08T17:58:40+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710003',
  'CL' => '0',
  'PACKID' => 28282,
  'STATUS' => '1',
)
error
[ 2025-08-08T17:58:40+08:00 ][ log ] 【命令响应】收到针对命令包 '28282' 的ACK确认
log
[ 2025-08-08T17:58:40+08:00 ][ log ] 【命令响应】成功更新命令 '28282' 的状态为 'acked'
log

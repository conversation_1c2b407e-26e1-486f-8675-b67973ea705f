<!--packageA/package/index.wxml-->
<view class="container" style="padding-bottom: {{safeBottom}}px;">
	<view class="package">
		<view class="store" bindtap="getStore">
			<view class="title">选择物业</view>
			<view class="nr">{{store_name?store_name:'请选择物业'}}</view>
		</view>
		<view class="state">
			<view class="title">套餐购买</view>
			<view class="list">
				<view class="nr {{setmeal_id == item.id?'active':''}}" bindtap="getPackage" data-id="{{item.id}}" wx:for="{{list}}">
					<view class="name">{{item.name}}</view>
					<view class="price">
						<view>￥</view>
						<text>{{item.price}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="instruction">
			<view class="title">购买套餐说明</view>
			<view class="xx">
				<rich-text nodes="{{instruction}}"></rich-text>
			</view>
		</view>
		<view class="pay">
			<view class="title">支付方式</view>
			<view class="check {{isCheck?'active':''}}" bindtap="getCheck">
				<image src="/image/icon_wx.png"></image>
				<view>微信支付</view>
			</view>
		</view>
		<view class="btn">
			<view class="check {{isAgreement?'active':''}}" bindtap="setAgreement">
				<view>阅读并同意</view>
				<text catchtap='getAgreement'>《购买协议》</text>
			</view>
			<view class="submit" bindtap="getSubmit">确认协议并支付</view>
		</view>


		<view class="tc" hidden="{{isShow}}">
			<view class="tcxy">
				<view class="close" bindtap="getclose">
					<image src="/image/icon_closeb.png"></image>
				</view>
				<view class="title">购买协议</view>
				<view class="desc">
					<rich-text nodes="{{Agreement}}"></rich-text>
				</view>
			</view>
		</view>
	</view>
</view>
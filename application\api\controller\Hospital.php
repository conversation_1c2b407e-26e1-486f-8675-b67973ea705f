<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;
use weixin\WeixinPay;
use app\api\controller\order\Order as ord;
use think\Db;

/**
 * 物业
 */
class Hospital extends Api {

    protected $noNeedLogin = ['*'];
    protected $noNeedRight = '*';

    public function _initialize() {
        parent::_initialize();
    }

    /*
     * 门店列表
     */

    public function lists() {
        $longitude = $this->request->param('longitude', null); //经度
        $latitude = $this->request->param('latitude', null); //纬度
        $page = $this->request->param('page', 1); //页码
        $limit = $this->request->param('limit', 10); //每页查询数量
        $name = $this->request->param('name', null); //门店名称
        if (!$longitude || !$latitude) {
            $this->error('参数错误');
        }
        $order = 'juli ASC';
        $field = 'getDistance(' . $longitude . ',' . $latitude . ',longitude,latitude) as juli,id,name,addr,logo_image,longitude,latitude';
        $where = [
            'status' => 1,
        ];
        if ($name) {
            $where['name'] = ['like', '%' . $name . '%'];
        }
        $list_obj = Db::name('hospital')
                ->field($field)
                ->where($where)
                ->order($order)
                ->paginate(array('list_rows' => $limit, 'page' => $page))
                ->toArray();
        $list = $list_obj['data'];
        foreach ($list as $k => $v) {
            $list[$k]['juli'] = round($list[$k]['juli'] / 1000, 2);
            $list[$k]['logo_image'] = 'https://' . $_SERVER['HTTP_HOST'] . $list[$k]['logo_image'];
            //车位共 可租借 【未实现】
            $list[$k]['intotal'] = Db::name('equipment')->where(['hospitals_id' => $v['id']])->count(); //总计车位数量
            $list[$k]['available'] = Db::name('equipment')->where(['hospitals_id' => $v['id'],'use_status' => 1,])->count(); //可用车位数量
        }
        $total = Db::name('hospital')->where($where)->count();
        $data['nextpage'] = ceil($total / $limit) > $page ? true : false;
        $data['list'] = $list;
        $this->success('加载成功', $data);
    }

    /**
     * 查询单条物业列表 
     */
    public function info() {
        $longitude = $this->request->param('longitude', null); //经度
        $latitude = $this->request->param('latitude', null); //纬度
        $hospital_id = $this->request->param('hospital_id', null); //物业id
        if ((!$longitude || !$latitude) && !$hospital_id) {
            $this->error('参数错误');
        }
        $where = [
            'status' => 1,
        ];
        $order = 'id desc';
        if ($hospital_id) {
            $field = 'id,name,addr,logo_image,longitude,latitude';
            $where['id'] = $hospital_id;
        } else {
            $order = 'juli ASC';
            $field = 'getDistance(' . $longitude . ',' . $latitude . ',longitude,latitude) as juli,id,name,addr,logo_image,longitude,latitude';
        }
        $info = db('hospital')->where($where)->field($field)->order($order)->find();
        if (!$info) {
            $this->error('无记录');
        }
        $this->success('加载成功', $info);
    }

}

const agreement = require('agreement.js');
const link = require('link.js');
const app = getApp()

//1、下发握手指令
export function woshou(that,i){
  console.log('第' + i + '次下发握手指令');
  var hex = 'BD11B1000102030405060708090A0B0C0D0E0F';
  var hex = hex + agreement.validityDomain(hex);
  agreement.sendOut(that,hex,function(e){
    //数据发送成功
    if(i < 2){
      i = i + 1;
      setTimeout(function(){
        woshou(that,i);
      },500);
    }else{
      console.log('握手两次发送完成');
      setTimeout(function(){
        shenqingyanzhengmima(that);
      },1000);
    }
  },function(e){
    //数据发送失败
    wx.hideLoading();
    wx.showToast({
      title: '下发握手指令失败',
      icon: 'none',
      duration: 2000
    })
    that.setData({
      bluetooth_type: 0,
    });
    // let params = {
    //   key_id: that.data.carid,
    //   behavior_type: 4,
    //   bluetooth_data: wx.getStorageSync("bluetooth_data"),
    //   status: 2,
    //   answer_data: '下发握手指令失败',
    // }
    // link.getueserMember(params)
  });
}


//2、申请验证密码指令
export function shenqingyanzhengmima(that) {
  wx.showLoading({
    title: '申请验证密码'
  })
  console.log('申请验证密码指令');
  var hex = 'BD11B2000102030405060708090A0B0C0D0E0F';
  var hex = hex + agreement.validityDomain(hex);
  agreement.sendOut(that, hex, function (e) {
    that.data.timer = setInterval(function () {
      if (that.data.bluetooth_type == 0) {
        wx.hideLoading();
        // that.setData({
        //   timer: ''
        // })
        clearInterval(that.data.timer)
        wx.showToast({
          title: '申请验证密码无应答',
          icon: 'none',
          duration: 2000
        })
        let params = {
          key_id: that.data.carid,
          behavior_type: 4,
          bluetooth_data: wx.getStorageSync("bluetooth_data"),
          status: 2,
          answer_data: '申请验证密码,设备无应答',
        }
        link.getueserMember(params)
      } else {

      }
    }, 20000)
  }, function (e) {
    let params = {
      key_id: that.data.carid,
      behavior_type: 4,
      bluetooth_data: wx.getStorageSync("bluetooth_data"),
      status: 2,
      answer_data: '申请验证密码指令数据发送失败',
    }
    link.getueserMember(params)
  });
}

//3、验证密码指令
export function yanzhengmima(that, str) {
  console.log('app发送密码进行验证');
  wx.showLoading({
    title: '密码验证中...'
  })
  var hex = 'BD11B4';
  var data = '7672667077642D06' + that.data.hex_equipment_password + str;
  var request_data = { 'step': 2, 'str': data, 'action': 'iOS_AES_Key5_Encryption', };
  app.post('index/aesjjm', request_data).then(res => {
    const {
      code,
      data,
      msg
    } = res
    //接口数据
    if (code == 1) {
      console.log(data)
      hex = hex + data;
      hex = hex + agreement.validityDomain(hex);
      agreement.sendOut(that, hex, function (e) {
        that.data.timer = setInterval(function () {
          if (that.data.bluetooth_type == 0) {
            wx.hideLoading();
            // that.setData({
            //   timer: ''
            // })
            clearInterval(that.data.timer)
            wx.showToast({
              title: '验证密码无应答',
              icon: 'none',
              duration: 2000
            })
            let params = {
              key_id: that.data.carid,
              behavior_type: 4,
              bluetooth_data: wx.getStorageSync("bluetooth_data"),
              status: 2,
              answer_data: '验证密码指令,设备无应答',
            }
            link.getueserMember(params)
          } else {
    
          }
        }, 20000)
      }, function (e) {
        let params = {
          key_id: that.data.carid,
          behavior_type: 4,
          bluetooth_data: wx.getStorageSync("bluetooth_data"),
          status: 2,
          answer_data: '验证密码指令数据发送失败',
        }
        link.getueserMember(params)
      });
    } else {
      wx.showToast({
        title: msg,
        icon: 'none',
        duration: 2000
      })
    }
  }).catch((err) => {

  })
}
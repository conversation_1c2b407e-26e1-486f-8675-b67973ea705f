// pages/index/login.js
const app = getApp()
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
    avatarUrl: defaultAvatarUrl,
    nickName: '',
    theme: wx.getSystemInfoSync().theme,
    type: '',// 1 首页 2 个人中心
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
    this.setData({
      type: options.type
    })
		wx.hideHomeButton()
    wx.onThemeChange((result) => {
      console.log(result)
      this.setData({
        theme: result.theme
      })
    })
  },
  
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail 
    this.setData({
      avatarUrl,
    })
    console.log(this.data.avatarUrl)
  },

  getName(e){
    console.log(e.detail.value)
    this.setData({
      nickName: e.detail.value
    })
  },

  getSubmit(){
    let that = this
    if(!that.data.nickName){
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none',
        duration: 2000
      })
      return
    }
    let params = {
      nickName: that.data.nickName,
      avatarUrl: that.data.avatarUrl,
    }
    app.post('user/profile', params).then(res => {
      const { code, data,msg} = res //接口数据
      if (code == 1) {
        wx.setStorageSync('nickname', that.data.nickName);
        wx.setStorageSync('avatar', that.data.avatarUrl);
        if(that.data.type == 1){
          wx.reLaunch({
            url: 'index',
          })
        }else{
          wx.reLaunch({
            url: 'my',
          })
        }
      }
    }).catch((err) => {

    })
  },

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
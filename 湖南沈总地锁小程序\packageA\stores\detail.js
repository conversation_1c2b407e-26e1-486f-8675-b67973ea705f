// packageA/stores/detail.js
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		id:'',
		allinfo:{
			image:'/image/banner.png',
			
		},
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		console.log('options',options);
		this.setData({
			id:options.id,
			allinfo:wx.getStorageSync('storeInfo'),
		})
		
	},
	onShow(){
		// this.getInfo();
	},
	// 获取信息
	getInfo(){
		app.post('Hospital/lists', {id:this.data.id}).then(res => {
			const {	code,	data,	msg} = res //接口数据
			if (code == 1) {
				this.setData({
					allinfo:data,
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {
			
		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {
		wx.removeStorageSync('storeInfo')
	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
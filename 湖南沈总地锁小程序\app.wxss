/**app.wxss**/
page {
  background: #FEF6AE;
}
view{
  box-sizing: border-box;
}
.level{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.level-two{
  display: flex;
  align-items: center;
}
.level-three{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  align-content: flex-start;
}
.padding-30{
  padding: 0 30rpx;
}
.btn-new{
	width: 100%;
	height: 113rpx;
	line-height: 113rpx;
	background: #F93F4E;
	border-radius: 59rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: 600;
	text-align: center;
	
	font-size: 64rpx;
	color: #FFFFFF;
}

.container {
  width: 100%;
  min-height: 100vh;
  background: #FEF6AE;
  box-sizing: border-box;
}

.navigator-hover {
  color: #818181;
  background-color: rgba(0, 0, 0, 0);
  opacity: 1;
}

.b-color{
	color: #2298F9 !important;
}
.o-color{
	color: #FB8F15 !important;
}

.zwsj {
	width: 100%;
	padding: 20% 0;
}

.zwsj image {
  width: 100%;
}

.zwsj view {
  font-size: 26rpx;
  line-height: 46rpx;
	color: #999;
	font-weight: bold;
  text-align: center;
}



.loading {
  width: 100%;
  align-items: center;
  justify-content: center;
  position: relative;
  text-align: center;
  font-size: 24rpx;
  line-height: 70rpx;
  color: #999;
}


.hoverbutn {
  opacity: 0.6;
}

.tc {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
}
.tc .close{
  width: 40rpx;
  height: 40rpx;
  right: 20rpx;
  top: 15rpx;
  position: absolute;
  z-index: 3;
}
.tc .close image{
  width: 100%;
  height: 100%;
}
.tc .title {
  font-size: 28rpx;
  line-height: 40rpx;
  color: #333;
  margin-bottom: 72rpx;
  text-align: center;
}
.tcxy{
  width: 90%;
  position: absolute;
  left: 5%;
  top: 20%;
  background: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  box-sizing: border-box;
  z-index: 2;
}
.tcxy .desc{
  font-size: 28rpx;
  line-height: 36rpx;
  color: #333;
  height: 660rpx;
  overflow-y: auto;
}

.tcmobile button{
  width: 80%;
  position: fixed;
  padding: 0;
  font-size: 32rpx;
  line-height: 100rpx;
  color: #fff;
  border-radius: 10rpx;
  background: #2298F9;
  top: 40%;
  left: 10%;
}


.pic_img {
  max-width: 100% !important;
  width: auto !important;
  margin: 0 auto;
}

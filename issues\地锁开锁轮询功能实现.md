# 地锁开锁轮询功能实现

## 任务描述
在小程序地锁页面（packageA/lockset/lockset）点击立即开锁按钮后，除了访问 orderUpdateStatus 方法外，还需要添加轮询功能查询 get_order_info 方法，返回查询成功后再跳转到订单详情页面。

## 实现方案
采用简单轮询方案：
- 轮询间隔：2秒
- 最大轮询次数：30次（约1分钟）
- 轮询接口：Order/get_order_info
- 成功条件：接口返回 code == 1

## 修改内容

### 1. 数据结构扩展
在 `data` 中添加轮询相关配置：
```javascript
pollCount: 0,        // 轮询次数
maxPollCount: 30,    // 最大轮询次数
pollInterval: 2000,  // 轮询间隔(毫秒)
```

### 2. orderUpdateStatus 方法修改
- 成功后不直接跳转页面
- 重置轮询计数器
- 调用 pollOrderStatus 方法开始轮询
- 增强错误处理

### 3. 新增 pollOrderStatus 方法
- 递归调用实现轮询
- 调用 Order/get_order_info 接口
- 成功时跳转到订单详情页
- 失败时继续轮询或提示用户
- 达到最大次数时提供重试选项

## 核心逻辑流程
1. 用户点击"立即开锁"按钮
2. 调用 orderUpdateStatus 发送开锁指令
3. 开锁指令成功后开始轮询
4. 每2秒调用 get_order_info 查询订单状态
5. 查询成功（订单状态为2）时跳转到订单详情页
6. 查询失败时继续轮询，直到成功或超时

## 错误处理
- 网络错误：继续重试轮询
- 超时处理：提示用户并提供重试选项
- 用户体验：保持加载状态，显示合适的提示信息

## 测试要点
1. 正常开锁流程测试
2. 网络异常情况测试
3. 超时情况测试
4. 用户取消操作测试

## 文件修改
- `packageA/lockset/lockset.js` - 主要逻辑实现
- `packageA/rental/detail.js` - 修复订单详情页数据显示问题

## 问题修复
### 问题描述
轮询功能正常，但跳转到订单详情页后数据不显示。

### 问题原因
订单详情页在 `onLoad` 时调用的是 `orderUpdateStatus` 方法，该方法用于发送开锁指令，成功时只返回 "ok"，不返回订单详细数据。而 `get_order_info` 方法才是用于查询订单详情的接口。

### 解决方案
将订单详情页的 `orderUpdateStatus` 方法改为 `getOrderInfo` 方法，调用 `Order/get_order_info` 接口获取完整的订单数据。

## 完成时间
2025-08-11

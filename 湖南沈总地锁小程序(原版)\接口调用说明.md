# 余额充值功能接口调用说明

## 后端接口

### 1. 获取用户信息（包含余额）
- **接口**: `User/getUserInfo`
- **方法**: POST
- **参数**: 无
- **返回**: 
```json
{
  "code": 1,
  "data": {
    "id": 123,
    "nickname": "用户昵称",
    "money": "66654.00",
    ...
  },
  "msg": "获取成功"
}
```

### 2. 获取余额变动记录
- **接口**: `User/getMoneyLog`
- **方法**: POST
- **参数**: 
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认20）
- **返回**:
```json
{
  "code": 1,
  "data": {
    "list": [
      {
        "id": 1,
        "money": "100.00",
        "type": 1,
        "memo": "余额充值",
        "createtime": 1640995200
      }
    ]
  },
  "msg": "获取成功"
}
```

### 3. 创建充值订单
- **接口**: `Order/rechargeCreate`
- **方法**: POST
- **参数**: 
  - `amount`: 充值金额（必填）
- **返回**:
```json
{
  "code": 1,
  "data": {
    "recharge_id": 123,
    "pay_id": 456,
    "order_sn": "RC20250131123456",
    "amount": "100.00"
  },
  "msg": "创建充值订单成功"
}
```

### 4. 发起充值支付
- **接口**: `Order/rechargePay`
- **方法**: POST
- **参数**: 
  - `pay_id`: 支付记录ID（必填）
- **返回**:
```json
{
  "code": 1,
  "data": {
    "timeStamp": "1640995200",
    "nonceStr": "abc123",
    "package": "prepay_id=wx123",
    "signType": "MD5",
    "paySign": "sign123"
  },
  "msg": "发起支付成功"
}
```

## 前端调用示例

### 使用app.post方法调用
```javascript
// 获取用户余额
app.post('User/getUserInfo', {}).then(res => {
  if (res.code == 1) {
    this.setData({
      userBalance: res.data.money
    })
  }
})

// 创建充值订单
app.post('Order/rechargeCreate', {
  amount: 100.00
}).then(res => {
  if (res.code == 1) {
    // 继续发起支付
    this.startPayment(res.data.pay_id)
  }
})
```

## 注意事项

1. **登录验证**: 所有接口都需要用户登录，确保token有效
2. **金额格式**: 金额统一使用字符串格式，保留两位小数
3. **错误处理**: 接口调用失败时要有适当的错误提示
4. **支付回调**: 支付成功后会自动处理余额更新，前端无需额外调用

## 数据库字段说明

### fa_user_money_log.type 字段含义
- 1: 充值
- 2: 提现
- 3: 购买套餐
- 4: 退款
- 5: 支付租金

### fa_pay.types 字段含义
- 1: 押金充值
- 2: 租用费用
- 3: 购买套餐
- 4: 余额充值（新增）

### fa_recharge_order.status 字段含义
- 1: 待支付
- 2: 已完成
- 3: 已取消

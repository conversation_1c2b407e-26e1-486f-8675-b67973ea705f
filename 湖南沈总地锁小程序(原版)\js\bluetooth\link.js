const common = require('common.js');
const agreement = require('agreement.js');
const app = getApp()

/**
 * 1.判断是否开启蓝牙
 * @param {*} that 
 */
export function openBluetooth(that) {
	let isContinue = true;
	wx.getSystemInfo({
		success(res) {
			let currVersion = res.system.split(' ')[1];
			wx.setStorageSync('platform', res.platform);
			if (res.platform == 'android' && common.versionCompare('6.5.7', currVersion)) {
				wx.showModal({
					title: '提示',
					content: '当前android版本过低，暂不支持',
					showCancel: false,
				})
				isContinue = false;
			} else if (res.platform == 'ios' && common.versionCompare('6.5.6', currVersion)) {
				console.log('提示:当前ios版本过低，暂不支持')
				wx.showModal({
					title: '提示',
					content: '当前ios版本过低，暂不支持',
					showCancel: false,
				})
				isContinue = false;
			}
		}
	})
	if (!isContinue) {
		return;
	}
	console.log("判断蓝牙是否开启中....");
	wx.openBluetoothAdapter({
		success: function (res) {
			console.log("开启蓝牙成功");
			getBluetoothAdapterState(that, res.platform);
		},
		fail: function (err) {
			console.log("开启蓝牙失败", err);
			wx.showModal({
				title: '提示',
				content: '蓝牙未开启，是否继续操作？',
				confirmText: '继续',
				cancelText: '返回',
				success(res) {
					if (res.confirm) {
						// 用户选择继续，隐藏加载提示
						wx.hideLoading();
						wx.showToast({
							title: '已跳过蓝牙连接，部分功能可能受限',
							icon: 'none',
							duration: 2000
						})
						that.setData({
							isBlue: true
						})
					} else if (res.cancel) {
						// 用户选择返回，清理蓝牙相关数据
						wx.hideLoading();
						that.setData({
							isBlue: true
						})
						wx.closeBLEConnection({
							deviceId: that.data.deviceId,
							success: (res) => {
								console.log('蓝牙断开成功1', res)
								wx.closeBluetoothAdapter({
									success(resp) {
										console.log('蓝牙断开成功2', resp)
										wx.setStorageSync('deviceId', '')
										that.setData({
											deviceName: '',
											deviceId: '',
											services: [],
											servicesUuid: '',
											characterWriteUuid: '',
											characterNotifyUuid: '',
										})
									}
								})
							},
							fail: function (res) {
								console.log('蓝牙断开失败1', res)
								wx.closeBluetoothAdapter({
									success(resp) {
										console.log('蓝牙断开成功2', resp)
										wx.setStorageSync('deviceId', '')
										that.setData({
											deviceName: '',
											deviceId: '',
											services: [],
											servicesUuid: '',
											characterWriteUuid: '',
											characterNotifyUuid: '',
										})
									}
								})
							}
						})
					}
				}
			})
		}
	});
}

/**
 * 2.判断蓝牙状态
 * @param {*} that 
 */
export function getBluetoothAdapterState(that) {
	console.log("开始获取蓝牙适配器状态");
	wx.getBluetoothAdapterState({
		success: function (res) {
			console.log("获取蓝牙适配器状态成功：" + JSON.stringify(res));
			let available = res.available; //蓝牙适配器是否可用 true 可用 false 不可用
			let discovering = res.discovering; //蓝牙是否正在搜索设备
			if (!available) {
				wx.showToast({
					title: '当前蓝牙不可用',
					icon: 'error',
				})
				return;
			}
			if (!discovering) {
				//开启搜索蓝牙
				wx.getSystemInfo({
					success: function (res) {
						startSearchBlueTooth(that);
						return
						if (res.platform == 'android') {
							console.log("android平台");
							createBLEConnection(that);
						} else {
							console.log("ios平台");
							startSearchBlueTooth(that);
						}
					},
				})
			}
		}
	})
}

/**
 * 3.开启搜索蓝牙
 * @param {*} that 
 */
export function startSearchBlueTooth(that) {
	console.log('开始搜索蓝牙中....');
	wx.startBluetoothDevicesDiscovery({
		powerLevel: "high",
		allowDuplicatesKey: false,
		services: [], //如果填写了此UUID，那么只会搜索出含有这个UUID的设备，建议一开始先不填写
		success: function (res) {
			console.log('startSearchBlueTooth成功' + JSON.stringify(res));
			if (res.isDiscovering) {
				wx.showLoading({
					title: '蓝牙搜索中',
					mask: true,
				})
				//获取设备列表
				onBluetoothDeviceFound(that);
			}
		},
		fail: function (res) {
			console.log('startSearchBlueTooth失败' + JSON.stringify(res));
		},
		complete: function (r) {

		}
	})
}

/**
 * 4.获取连接蓝牙列表
 * @param {*} that 
 */
export function onBluetoothDeviceFound(that) {
	console.log('蓝牙名称：', that.data.deviceName)
	console.log('设备Mac地址:' + that.data.deviceId);
	// let nlist = [];
	let times = 60;
	// let timer = setInterval(function () {
	// 	times--
	// 	if (times == 0) {
	// 		wx.stopBluetoothDevicesDiscovery({
	// 			success: function (res) {
	// 				clearInterval(timer)
	// 				for (var l = 0; l < nlist.length; l++) {
	// 					if (nlist.localName == that.data.deviceName) {
	// 						that.setData({
	// 							deviceId: nlist[l].deviceId,
	// 						})
	// 						onBluetoothDeviceFound(that)
	// 					} else {
	// 						if (i == nlist.length - 1) {
	// 							wx.showModal({
	// 								title: '提示',
	// 								content: '未搜索到相关蓝牙设备，是否继续搜索',
	// 								cancelText: '否',
	// 								confirmText: '是',
	// 								success(res) {
	// 									if (res.confirm) {
	// 										onBluetoothDeviceFound(that)
	// 									} else if (res.cancel) {
	// 										wx.navigateBack({
	// 											delta: 1,
	// 										})
	// 									}
	// 								}
	// 							})
	// 						}
	// 					}
	// 				}
	// 			}
	// 		});
	// 	}
	// }, 1000)

	// wx.onBluetoothDeviceFound(function (resp) {
	// 	wx.getBluetoothDevices({
	// 		success: function (res) {
	// 			console.log('搜索到的蓝牙列表~~')
	// 			var list = res.devices
	// 			nlist = list
	// 			console.log(nlist)
	// 		},
	// 	})
	// })

	
	let timer = setInterval(function () {
		times--
		if (times == 0) {
			wx.stopBluetoothDevicesDiscovery({
				success: function (res) {
					clearInterval(timer)
				}
			});
			wx.showModal({
				title: '提示',
				content: '未搜索到相关蓝牙设备，是否继续搜索',
				cancelText: '否',
				confirmText: '是',
				success(res) {
					if (res.confirm) {
						onBluetoothDeviceFound(that)
					} else if (res.cancel) {
						wx.navigateBack({
							delta: 1,
						})
					}
				}
			})
		}
	}, 1000)

	wx.onBluetoothDeviceFound(function (resp) {
		console.log('onBluetoothDeviceFound获取蓝牙列表数据', resp.devices[0])
		if (resp.devices[0].localName == that.data.deviceName) {
			clearInterval(timer)
			that.setData({
				deviceId: resp.devices[0].deviceId,
			})
			console.log('获取到的deviceId', that.data.deviceId)
			wx.stopBluetoothDevicesDiscovery({
				success: function (res) {
					console.log("停止蓝牙搜索");
					createBLEConnection(that);
				}
			});
		}
	})
}

// 断开蓝牙
export function closeBLEConnection(e, fuc, fai) {
	var deviceId = e;
	wx.closeBLEConnection({
		deviceId: deviceId,
		success: (res) => {
			fuc(res);
		},
		fail: function (res) {
			fai(res);
		}
	})
}

/**
 * 3.开始链接蓝牙
 * @param {*} that 
 */
export function createBLEConnection(that) {
	console.log('开始链接蓝牙');
	wx.showLoading({
		title: '蓝牙连接中...',
		mask: true,
	})
	var deviceId = that.data.deviceId;
	console.log('设备Mac地址:' + deviceId);
	wx.createBLEConnection({
		deviceId: deviceId,
		success: (res) => {
			console.log('蓝牙连接成功');
			getBLEDeviceServices(that)
		},
		fail: function (res) {
			wx.showToast({
				title: '蓝牙连接失败',
				icon: 'none',
				duration: 2000
			})
			wx.hideLoading();
			that.getcloseBLE();
			setTimeout(function () {
				wx.switchTab({
					url: '/pages/index/index'
				})
			}, 2000);
			console.log('蓝牙连接失败' + JSON.stringify(res));
		}
	})
}



/**
 * 4、获取蓝牙设备所有服务
 * @param {*} that 
 */
export function getBLEDeviceServices(that) {
	console.log('开始获取蓝牙服务');
	var deviceId = that.data.deviceId;
	wx.getBLEDeviceServices({
		deviceId: deviceId,
		success: (res) => {
			console.log('蓝牙服务');
			console.log(res);
			that.setData({
				services: res.services,
				servicesUuid: res.services[0].uuid,
			});
			getBLEDeviceCharacteristics(that);
		},
		fail: function (res) {
			wx.hideLoading();
			wx.showToast({
				title: '获取蓝牙服务失败',
				icon: 'none',
				duration: 2000
			})
			wx.switchTab({
				url: '/pages/index/index',
			})
			console.log('获取蓝牙服务失败' + JSON.stringify(res));
		}
	})
}

/**
 * 5、获取蓝牙设备某个服务中所有特征值
 * @param {*} that 
 */
export function getBLEDeviceCharacteristics(that) {
	console.log('获取蓝牙设备某个服务中所有特征值');
	wx.getBLEDeviceCharacteristics({
		// 这里的 deviceId 是 蓝牙设备 id 需要在上面的 getBluetoothDevices
		deviceId: that.data.deviceId,
		// 这里的 serviceId 是 蓝牙服务 UUID 需要在上面的 getBLEDeviceServices 接口中获取
		serviceId: that.data.servicesUuid,
		success: function (res) {
			console.log(res);
			that.setData({
				characterWriteUuid: res.characteristics[1].uuid,
				characterNotifyUuid: res.characteristics[0].uuid,
			});
			console.log("订阅特征值 deviceId:" + that.data.deviceId + "   serviceId:" + that.data.servicesUuid + "   characteristicId:" + that.data.characterNotifyUuid);
			wx.notifyBLECharacteristicValueChange({
				state: true,
				deviceId: that.data.deviceId, //蓝牙设备 id
				serviceId: that.data.servicesUuid, //蓝牙特征值对应服务的 uuid  //that.data.ceshiData[that.data.ceshiKey].notifyFuwuUuid , 
				characteristicId: that.data.characterNotifyUuid, //蓝牙特征值的 uuid  // that.data.ceshiData[that.data.ceshiKey].notifyTezhengUuid , 
				success: function (res) {
					// success
					console.log('订阅特征值成功：', res);
					onBLEConnectionStateChanged(that); //添加状态变化事件
					// wx.hideLoading();
					// wx.showToast({
					// 	title: '蓝牙连接成功',
					// 	icon: 'none',
					// 	duration: 2000
					// })
					agreement.sendOut(that, 3, function (e) {
						//数据发送成功
					}, function (e) {
						//数据发送失败
						wx.hideLoading();
						wx.showToast({
							title: '蓝牙连接失败',
							icon: 'none',
							duration: 2000
						})
						wx.switchTab({
							url: '/pages/index/index',
						})
					});
				},
				fail: function (res) {
					wx.hideLoading();
					wx.showToast({
						title: '蓝牙连接失败',
						icon: 'none',
						duration: 2000
					})
					wx.switchTab({
						url: '/pages/index/index',
					})
					// fail
					console.log('订阅特征值失败', res);
				},
				complete: function (res) {
					// complete
					/**
					 * 回调获取 设备发过来的数据
					 */
					console.log("回调获取，设备发来的数据");
					onBLECharacteristicValueChange(that);
				}
			})
		},
		fail: function (res) {
			wx.hideLoading();
			wx.showToast({
				title: '蓝牙连接失败',
				icon: 'none',
				duration: 2000
			})
			wx.switchTab({
				url: '/pages/index/index',
			})
			console.log('订阅蓝牙特征值失败');
			console.log(res);
		}
	})
}

/**
 * 6-1、监听蓝牙链接状态的改变时间
 * @param {*} that 
 */
export function onBLEConnectionStateChanged(that) {
	console.log('监听蓝牙低功耗连接状态的改变事件。包括开发者主动连接或断开连接，设备丢失，连接异常断开等等');
	wx.onBLEConnectionStateChange(function (res) {
		console.log('蓝牙链接状态发生变化');
		console.log(res.connected);
		if (res.connected == false) {
			wx.setStorageSync('deviceId', '');
			wx.showToast({
				title: '蓝牙设备断开',
				icon: 'none',
				duration: 2000
			});
		}
	})
}

/**
 * 6-2、监听低功耗蓝牙设备的特征值变化事件
 * @param {*} that 
 */
export function onBLECharacteristicValueChange(that) {
	console.log('监听蓝牙低功耗设备的特征值变化事件');
	wx.onBLECharacteristicValueChange(function (characteristic) {
		console.log('接收到数据');
		console.log(characteristic.value);
		var hex = agreement.buffer2hex(characteristic.value);
		var hexArr = hex.split(","); //数据域的数组格式
		var type = hexArr[2];
		console.log(type);
		console.log('数据域', hexArr);
		if (type == '01') {
			wx.hideLoading();
			var params = {
				equipment_info_id: that.data.equipment.id,
				power: hexArr[4],
			};
			app.post('Ajax/canzy', params).then(res => {
				const {
					code,
					data,
					msg
				} = res //接口数据
				if (code == 1) {
					wx.setStorageSync('deviceId', that.data.deviceId);
					wx.showToast({
						title: '蓝牙连接成功',
						icon: 'none',
						duration: 2000
					})
					that.setData({
						isBlue: false
					})
				} else {
					wx.showToast({
						title: msg,
						icon: 'none',
						duration: 2000
					})
				}
			}).catch((err) => {

			})
		} else if (type == '03') {
			console.log('设备降下设备应答');
			if (hexArr[4] == '00') {
				// 成功
				wx.hideLoading();
				wx.showToast({
					title: '开锁成功',
					icon: 'none',
					duration: 2000
				})
				if (that.data.type == 1) {
					that.Order()
				} else {
					that.orderUpdateStatus()
				}
			} else {
				// 失败
				wx.hideLoading();
				wx.showToast({
					title: '开锁失败',
					icon: 'none',
					duration: 2000
				})
				that.setData({
					isUnLock: 1,
				});
			}
		} else if (type == '05') {
			console.log('设备升起应答');
			if (hexArr[4] == '00') {
				// 成功
				wx.hideLoading();
				wx.showToast({
					title: '关锁成功',
					icon: 'none',
					duration: 2000
				})
				that.setData({
					isLock: 3,
				});
				that.getcloseBLEConnection()
			} else {
				// 失败
				wx.hideLoading();
				wx.showToast({
					title: '关锁失败',
					icon: 'none',
					duration: 2000
				})
				that.setData({
					isLock: 1,
				});
			}
		}
	})
}
/* packageA/package/stores.wxss */
.container{
	background: #fff;
	padding: 0 30rpx;
}
.search{
	position: fixed;
	width: 100%;
	background: #fff;
	left: 0;
	top: 0;
	padding: 20rpx 30rpx 0;
	box-sizing: border-box;
}
.search view{
	background: #F5F5F5;
	border-radius: 30rpx;
	padding: 0 24rpx;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}
.search view image{
	width: 28rpx;
	height: 28rpx;
}
.search view input{
	font-size: 26rpx;
	line-height: 64rpx;
	height: 64rpx;
	color: #999;
	margin-left: 12rpx;
	width: calc(100% - 40rpx);
}
.list{
	padding: 84rpx 10rpx 0;
}
.list .nr{
	padding: 40rpx 0;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #EDEDED;
}
.list .nr .pic{
	width: 130rpx;
	height: 130rpx;
	border-radius: 10rpx;
	overflow: hidden;
}
.list .nr .pic image{
	width: 100%;
	height: 100%;
}
.list .nr .text{
	width: calc(100% - 150rpx);
}
.list .nr .text .name{
	font-size: 32rpx;
	line-height: 46rpx;
	color: #333;
	margin-bottom: 16rpx;
}
.list .nr .text .numb{
	font-size: 26rpx;
	line-height: 30rpx;
	color: #999;
	margin-bottom: 16rpx;
	padding-left: 32rpx;
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAeCAYAAAAy2w7YAAAAAXNSR0IArs4c6QAAAlVJREFUSEvtlk9o1EAUxr83yWEFkSJClwXBelLBPyAVEUFBelIR0V3wVkSYjYEgBY9iELxU8A+aTcbiRQTFgl4KPe3Bo6cqPdiTlCLs6kGxINQlM09m2ZW2rG02tOChD3JJ3vf9Zl4meY/QI5RSu7TWw47jHAawD8BBADsA7OxcYOZ5IkoBzDPzDIBPQoiZgYGB2UqlolfbUvdGrVbbS0SXiegSER3rtYAs95h5kYimtNaTaZpOB0Hw2+poYmJiv9b6KRGdzGLUTw4z/wRwv16v3yWl1GcAQ/0Y9JvLzFctiPsV5sgPt0A5qtaW/KelY+a3rVbrSqFQOG6MmSaibRm32NeOfkkpt8dxPJqm6TvXdc8LIR5tBuhro9EoFYvF5wAeCiEOMfOzzQC1X6qU8k4URYOu634AMLhZIBhjhojoNhGNZoTkO3VboE55Q0qSZIGIdmetd87SjVnQpG14WUFCiDPGmDEAZ7NqWq3WMMVxfFEI8SarKEfenJTygG3ltlW8BzCcwySLpCKlnGzPDFEUHXFd1w4YGxrMPNVsNi+EYWj+DidKqRsAHmwg6Ysx5qjned/aw8ly4yRJIiK6vgGwHwBOSCnnul4rQOVy2RkZGRlnZnuqckVn8jlVrVY/LjdYAeo+UEr5AJ7kIM0COCelXFit7QmySXEcnxZCvMr6hyail0tLS9UgCBZ7LfCfoM5pLDqO83idD/q7McbzPO/1WhVYE9QVJkliR+V7APasMnuRpulN3/eb65U5E8iahGFYKJVK0hhzzQ72Wutbvu/b5pcp/gBUXQRFkThAqAAAAABJRU5ErkJggg==) no-repeat left center;
	background-size: 26rpx 30rpx;
}
.list .nr .text .address{
	font-size: 24rpx;
	line-height: 34rpx;
	color: #999;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
/* packageB/order/details.wxss */
.detail{
	background: linear-gradient(180deg, #2DA7F6 0%, rgba(45,167,246,0.3900) 61%, rgba(170,204,250,0) 100%);
	padding: 20rpx 30rpx 0;
}
.detail .top{
	margin-bottom: 26rpx;
}
.detail .top view{
	font-size: 36rpx;
	line-height: 50rpx;
	color: #fff;
	font-weight: bold;
	margin-bottom: 12rpx;
}
.detail .top text{
	font-size: 26rpx;
	line-height: 36rpx;
	color: #fff;
}
.detail .order{
	background: #fff;
	border-radius: 10rpx;
	padding: 30rpx 35rpx;
	margin-bottom: 20rpx;
}
.detail .order .title{
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-bottom: 12rpx;
}
.detail .order .title .name{
	font-size: 36rpx;
	line-height: 50rpx;
	color: #333;
	font-weight: bold;
}
.detail .order .title .price{
	font-size: 38rpx;
	line-height: 50rpx;
	color: #333;
	font-weight: bold;
}
.detail .order .xx{
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: flex-end;
}
.detail .order .xx .adtim{
	width: 450rpx;
}
.detail .order .xx .adtim view{
	font-size: 26rpx;
	line-height: 36rpx;
	color: #333;
	margin-bottom: 15rpx;
}
.detail .order .xx .adtim text{
	font-size: 24rpx;
	line-height: 30rpx;
	color: #999;
	display: block;
}
.detail .order .xx .btn{
	font-size: 28rpx;
	line-height: 60rpx;
	color: #2298F9;
	border: 1rpx solid #2298F9;
	border-radius: 30rpx;
	padding: 0 24rpx;
}
.detail .order_xx{
	background: #fff;
	border-radius: 10rpx;
	padding: 0 20rpx 40rpx;
}
.detail .order_xx .title{
	font-size: 30rpx;
	line-height: 95rpx;
	color: #333;
	font-weight: bold;
	border-bottom: 1rpx solid #EDF1F7;
	padding: 0 16rpx;
	margin-bottom: 30rpx;
}
.detail .order_xx .nr .xx{
	display: flex;
	flex-wrap: wrap;
	margin-top: 20rpx;
	padding: 0 12rpx;
}
.detail .order_xx .nr .xx view{
	font-size: 26rpx;
	line-height: 36rpx;
	color: rgba(51, 51, 51, 0.6);
}
.detail .order_xx .nr .xx text{
	font-size: 26rpx;
	line-height: 36rpx;
	color: #333;
}
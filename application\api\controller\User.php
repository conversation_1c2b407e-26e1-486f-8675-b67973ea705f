<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\library\Wechat;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;
use weixin\Wechatapppay;

/**
 * 会员接口
 */
class User extends Api {

    protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third', 'wxxcxlogin'];
    protected $noNeedRight = '*';

    public function _initialize() {
        parent::_initialize();
    }

    /**
     * 会员中心
     */
    public function index() {
        $this->success('', ['welcome' => $this->auth->nickname]);
    }

    /**
     * 会员登录
     *
     * @param string $account  账号
     * @param string $password 密码
     */
    public function login() {
        $account = $this->request->request('account');
        $password = $this->request->request('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 手机验证码登录
     *
     * @param string $mobile  手机号
     * @param string $captcha 验证码
     */
    public function mobilelogin() {
        $mobile = $this->request->request('mobile');
        $captcha = $this->request->request('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
            $this->error(__('Captcha is incorrect'));
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
        }
        if ($ret) {
            Sms::flush($mobile, 'mobilelogin');
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注册会员
     *
     * @param string $username 用户名
     * @param string $password 密码
     * @param string $email    邮箱
     * @param string $mobile   手机号
     */
    public function register() {
        $username = $this->request->request('username');
        $password = $this->request->request('password');
        $email = $this->request->request('email');
        $mobile = $this->request->request('mobile');
        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
        if ($email && !Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        $ret = $this->auth->register($username, $password, $email, $mobile, []);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注销登录
     */
    public function logout() {
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 会员个人信息
     * GET请求：查询用户信息
     * POST请求：修改用户信息
     *
     * @param string $avatar   头像地址
     * @param string $username 用户名
     * @param string $nickname 昵称
     * @param string $bio      个人简介
     */
    public function profile() {
        if ($this->request->isGet()) {
            // GET请求：查询用户信息
            $userinfo = $this->auth->getUserinfo();
            $this->success('获取成功', $userinfo);
        } else {
            // POST请求：修改用户信息
            $user = $this->auth->getUser();
            $username = $this->request->param('username');
            $nickname = $this->request->param('nickName');
            $bio = $this->request->param('bio');
            $avatar = $this->request->param('avatarUrl', '', 'trim,strip_tags,htmlspecialchars');
            if ($username) {
                $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
                if ($exists) {
                    $this->error(__('Username already exists'));
                }
                $user->username = $username;
            }
            $user->nickname = $nickname;
            $user->bio = $bio;
            $user->avatar = $avatar;
            $user->save();
            $this->success();
        }
    }

    /**
     * 修改邮箱
     *
     * @param string $email   邮箱
     * @param string $captcha 验证码
     */
    public function changeemail() {
        $user = $this->auth->getUser();
        $email = $this->request->post('email');
        $captcha = $this->request->request('captcha');
        if (!$email || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if (\app\common\model\User::where('email', $email)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Email already exists'));
        }
        $result = Ems::check($email, $captcha, 'changeemail');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->email = 1;
        $user->verification = $verification;
        $user->email = $email;
        $user->save();

        Ems::flush($email, 'changeemail');
        $this->success();
    }

    /**
     * 修改手机号 启用
     *
     * @param string $email   手机号
     * @param string $captcha 验证码
     */
    public function changemobile() {
        $user = $this->auth->getUser();
        $mobile = $this->request->request('mobile');
        $captcha = $this->request->request('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Mobile already exists'));
        }
        $result = Sms::check($mobile, $captcha, 'changemobile');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->mobile = 1;
        $user->verification = $verification;
        $user->mobile = $mobile;
        $user->save();

        Sms::flush($mobile, 'changemobile');
        $this->success();
    }

    /**
     * 第三方登录
     *
     * @param string $platform 平台名称
     * @param string $code     Code码
     */
    public function third() {
        $url = url('user/index');
        $platform = $this->request->request("platform");
        $code = $this->request->request("code");
        $config = get_addon_config('third');
        if (!$config || !isset($config[$platform])) {
            $this->error(__('Invalid parameters'));
        }
        $app = new \addons\third\library\Application($config);
        //通过code换access_token和绑定会员
        $result = $app->{$platform}->getUserInfo(['code' => $code]);
        if ($result) {
            $loginret = \addons\third\library\Service::connect($platform, $result);
            if ($loginret) {
                $data = [
                    'userinfo' => $this->auth->getUserinfo(),
                    'thirdinfo' => $result
                ];
                $this->success(__('Logged in successful'), $data);
            }
        }
        $this->error(__('Operation failed'), $url);
    }

    /**
     * 重置密码
     *
     * @param string $mobile      手机号
     * @param string $newpassword 新密码
     * @param string $captcha     验证码
     */
    public function resetpwd() {
        $type = $this->request->request("type");
        $mobile = $this->request->request("mobile");
        $email = $this->request->request("email");
        $newpassword = $this->request->request("newpassword");
        $captcha = $this->request->request("captcha");
        if (!$newpassword || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if ($type == 'mobile') {
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
        } else {
            if (!Validate::is($email, "email")) {
                $this->error(__('Email is incorrect'));
            }
            $user = \app\common\model\User::getByEmail($email);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Ems::check($email, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Ems::flush($email, 'resetpwd');
        }
        //模拟一次登录
        $this->auth->direct($user->id);
        $ret = $this->auth->changepwd($newpassword, '', true);
        if ($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 微信小程序登录
     * @return type
     */
    public function wxxcxlogin() {
        $code = $this->request->param('code');
        $nickName = $this->request->param('nickName');
        $avatarUrl = $this->request->param('avatarUrl');
        $gender = $this->request->param('gender');
        $payconfig = config('wxali.wx')['xcx'];
        $param['appid'] = $payconfig['appid'];
        $param['secret'] = $payconfig['appsecret'];
        //小程序登录的id 
        $param['js_code'] = $code;
        $param['grant_type'] = 'authorization_code';
        $http_key = httpCurl('https://api.weixin.qq.com/sns/jscode2session', $param, 'GET');
        $session_key = json_decode($http_key, true);
        if (!empty($session_key['session_key'])) {
            $userInfo = db('user')->where(['openid' => $session_key['openid']])->find();
            if (!$userInfo) {
                //用户不存在
                //新增会员
                $salt = Random::alnum();
                $user_data = array(
                    'group_id' => 1,
                    'username' => $this->getUsername(),
                    'nickname' => $nickName,
                    'password' => $this->auth->getEncryptPassword('123456', $salt),
                    'salt' => $salt,
                    'avatar' => $avatarUrl,
                    'gender' => $gender,
                    'joinip' => request()->ip(),
                    'jointime' => time(),
                    'createtime' => time(),
                    'status' => 'normal',
                    'openid' => $session_key['openid'],
                    'session_key' => $session_key['session_key'],
                );
                $user_id = db('user')->insertGetId($user_data);
                if (!$user_id) {
                    $this->error('用户信息入库失败');
                }
            } else {
                //用户已存在
                //更新会员公众号信息
                $user_update_data = array(
                    'nickname' => $nickName,
                    'avatar' => $avatarUrl,
                    'gender' => $gender,
                    'updatetime' => time(),
                    'session_key' => $session_key['session_key'],
                );
                db('user')->where(['id' => $userInfo['id']])->update($user_update_data);
                $user_id = $userInfo['id'];
            }
            $this->auth->direct($user_id);
            $userInfo = $this->auth->getUserinfo();
            $this->success('返回成功', $userInfo);
        } else {
            $this->error('获取session_key失败！');
        }
    }

    /**
     * 获得微信手机号（新版方式）
     * 使用微信官方推荐的 code 换取手机号方式
     */
    public function wxUpdateMobile() {
        // 获取前端传递的动态令牌code
        $code = $this->request->param('code', null);

        // 验证参数
        if (empty($code)) {
            $this->error('参数错误：缺少code参数');
        }

        try {
            // 实例化微信工具类
            $wechat = new Wechat();

            // 调用微信API获取手机号信息
            $phoneInfo = $wechat->getMiniPhoneNumber($code);

            if (!$phoneInfo) {
                $this->error('获取手机号失败，请重试');
            }

            // 提取手机号（优先使用纯手机号，如果没有则使用完整手机号）
            $mobile = $phoneInfo['purePhoneNumber'] ?? $phoneInfo['phoneNumber'] ?? '';

            if (empty($mobile)) {
                $this->error('手机号为空，请重试');
            }

            // 更新用户手机号到数据库
            $user_data = [
                'mobile' => $mobile,
                'updatetime' => time(),
            ];

            $res = db('user')->where(['id' => $this->auth->id])->update($user_data);
        } catch (\Exception $e) {
            // 记录异常日志
            trace([
                'user_id' => $this->auth->id,
                'code' => $code,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], '获取微信手机号异常');

            $this->error('系统异常，请稍后重试');
        }
        if ($res) {

            $this->success('更新手机号成功', $mobile);
        } else {
            $this->error('更新手机号失败，请重试');
        }



    }

    /*
     * 获得一个随机用户名
     */

    public function getUsername() {
        $str = 'hskh_' . getRandomStr(6);
        if (db('user')->where(['username' => $str])->find()) {
            $str = $this->getUsername();
        }
        return $str;
    }

    /*
     * 判断会员是否可以借床
     */

    public function getDefault() {
        $user = db('user')->where(['id' => $this->auth->id])->find();
        //if ($user['is_maintain'] != 1) {
        //    $this->success('维护员');
        //    return;
        //}
//        $deposit = $user['deposit'];
//        $deposit_id = $user['deposit_id'];
//        if ($deposit_id > 0) {
//            //会员表中有充值记录id
//            $pay = db('pay')->where(['id' => $deposit_id])->find();
//            if ($pay['status'] == 5) {
//                //退款中
//                $this->error('未充值保证金', '', 201);
//            } else {
                //充值记录正常
                //验证会员是否有未完成的订单
                $data = [
                    'code' => 1,//1 可以租床 202 有未结束订单
                ];
                $order = db('order')->where(['user_id' => $this->auth->id, 'status' => ['<', 3], 'use_status' => 2])->find();
                if ($order) {
                    $order['equipment'] = db('equipment')->where(['id' => $order['equipment_id']])->find();
                    $data['code'] = 202;
                    $data['order'] = $order;
//                    $this->error('您有未完成的订单', $order, 202);
                }
                $this->success('加载成功',$data);
//            }
//        } else {//无充值记录
//            $this->error('未充值保证金', '', 201);
//        }
    }

    /*
     * 修改手机号[弃用]
     */

    public function updateMobile() {
        $mobile = input('mobile', NULL);
        $code = input('code', NULL);
        if ($mobile != null && $code != null) {
            $sms = db('sms')->where(['mobile' => $mobile])->order('id desc')->find();
            if ($sms) {
                if ($sms['code'] == $code) {
                    $time = time();
                    if (($time - 600 ) <= $sms['createtime']) {
                        $user_data = array(
                            'mobile' => $mobile,
                        );
                        $res = db('user')->where(['id' => $this->uid])->update($user_data);
                        if ($res) {
                            $this->success('更新成功', $mobile);
                        } else {
                            $this->error('更新失败');
                        }
                    } else {
                        $this->error('验证码过期');
                    }
                } else {
                    $this->error('验证码错误');
                }
            } else {
                $this->error('无验证码');
            }
        } else {
            $this->error('参数错误');
        }
    }

    /**
     * 查询故障类型
     */
    public function faulttypes() {
        $list = db('fault_types')->where('1=1')->field('id,info')->select();
        $this->success('加载成功', $list);
    }

    /*
     * 故障上报
     */

    public function faultAdd() {
        $kpl = input('kpl', null);
        $content = input('content', null);
        $fault_types_id = input('fault_types_id');
        if ($kpl) {
            $equipment_info = db('equipment_info')->where(['kpl' => $kpl])->find();
            if ($equipment_info) {

                $info = $this->equipment_info_lujing($equipment_info);

                $fault_data = array(
                    'user_id' => $this->uid,
                    'platform_id' => $equipment_info['platform_id'],
                    'agent_id' => $equipment_info['agent_id'],
                    'hospital_id' => $equipment_info['hospital_id'],
                    'departments_id' => $equipment_info['departments_id'],
                    'equipment_id' => $equipment_info['equipment_id'],
                    'equipment_info_id' => $equipment_info['id'],
                    'lujing' => $info,
                    'content' => $content,
                    'status' => 1,
                    'createtime' => time(),
                    'updatetime' => time(),
                    'fault_types_id' => $fault_types_id,
                );
                $res = db('fault')->insertGetId($fault_data);
                if ($res) {
                    $this->success('上报成功');
                } else {
                    $this->error('上报失败');
                }
            } else {
                $this->error('设备不存在');
            }
        } else {
            $this->error('参数错误');
        }
    }

    //查询用户是否已经缴纳保证金
    public function userDeposit() {
        $return = array(
            'is_deposit' => 1, //1已充值 2 未充值
        );
        $user = db('user')->where(['id' => $this->uid])->find();
        if ($user['deposit'] > 0) {
            $return['deposit'] = $user['deposit'];
        } else {
            $return['is_deposit'] = 2;
        }
        $this->success('加载成功', $return);
    }

    //修改用户订阅消息状态
    public function userSubscribe() {
        $is_subscribe = input('is_subscribe', NULL);
        $res = db('user')->where(['id' => $this->uid])->update(['is_subscribe' => $is_subscribe, 'subscribetime' => time()]);
        $this->success('加载成功');
    }

    //退款
    public function depositTixian() {
        $user = db('user')->where(['id' => $this->uid])->find();
        if ($user['deposit'] > 0) {
            $order = db('order')->where(['user_id' => $this->uid, 'status' => ['<>', 3]])->find();
            if (!$order) {
                $pay = db('pay')->where(['id' => $user['deposit_id']])->find();
                if ($pay) {
                    if ($pay['status'] == 2) {
                        $return = $this->wxRefund($pay);
                        if ($return['success'] == true) {
                            $this->success($return['msg']);
                        } else {
                            $this->error($return['msg']);
                        }
                    } else {
                        if ($pay['status'] == 1) {
                            $this->error('未支付');
                        } else if ($pay['status'] == 3) {
                            $this->error('已退款');
                        }
                    }
                } else {
                    $this->error('支付记录不存在');
                }
            } else {
                $this->error('有未完成订单');
            }
        } else {
            $this->error('未充值保证金');
        }
    }

    //发起退款
    //发起退款
    public function wxRefund($pay) {
//        $pay = db('pay')->where(['id' => 6834])->find();
        $return = array(
            'success' => FALSE,
        );
        $wxappid = $this->wx_config['appid'];
        $mch_id = $this->wx_config['mch_id'];
        $notify_url = $this->wx_config['refundNotify_url'];
        $wxkey = $this->wx_config['wxkey'];
        $apiclient_cert = $this->wx_config['apiclient_cert'];
        $apiclient_key = $this->wx_config['apiclient_key'];

        $wechatAppPay = new wechatapppay($wxappid, $mch_id, $notify_url, $wxkey, $apiclient_cert, $apiclient_key);
        //查询订单状态
        $sn = $pay['sn'];
        $transaction_id = $pay['transaction_id'];

        $wx_orderQuery = $wechatAppPay->orderQuery($sn);
        if ($wx_orderQuery['return_code'] == 'SUCCESS') {
            if ($wx_orderQuery['trade_state'] == 'SUCCESS') {
                $money = $wx_orderQuery['total_fee'];

                //退款
                $params = array();
                $params['out_trade_no'] = $sn;            //必填项 自定义的订单号
                $params['total_fee'] = $money;       //必填项 订单金额 单位为分所以要*100
                $params['return_oid'] = $transaction_id;
                $params['notify_url'] = $this->wx_config['refundNotify_url']; //退款单号
                $wx_result = $wechatAppPay->refund($params);

                if ($wx_result['return_code'] == 'SUCCESS') {
                    db('pay')->where(['id' => $pay['id']])->update(array(
                        'status' => 5,
                    ));
                    $return['msg'] = '退款成功，等待处理';
                    $return['success'] = true;
                } else {
                    $return['msg'] = '通信失败，错误原因：' . $wx_result['return_msg'];
                }
            } else {
                switch ($wx_orderQuery['trade_state']) {
                    case 'REFUND':
                        $return['msg'] = '已退款';
                        break;
                    case 'NOTPAY':
                        $return['msg'] = '未支付';
                        break;
                    case 'CLOSED':
                        $return['msg'] = '已关闭';
                        break;
                    case 'REVOKED':
                        $return['msg'] = '已撤销';
                        break;
                    case 'USERPAYING':
                        $return['msg'] = '用户支付中';
                        break;
                    case 'PAYERROR':
                        $return['msg'] = '支付失败';
                        break;
                    default:
                        $return['msg'] = '状态错误';
                        break;
                }
            }
        } else {
            $return['msg'] = '通信失败，错误原因：' . $wx_orderQuery['return_msg'];
        }
        return $return;
    }

    /**
     * 测试微信工具类功能（仅用于开发调试）
     * 可以通过此接口测试access_token获取和缓存功能
     */
    public function testWechatTool() {
        try {
            $wechat = new Wechat();

            // 获取微信小程序配置信息
            $miniConfig = $wechat->getMiniConfig();

            // 获取access_token
            $accessToken = $wechat->getMiniAccessToken();

            $result = [
                'config' => $miniConfig,
                'access_token' => $accessToken ? '获取成功' : '获取失败',
                'cache_test' => '缓存功能正常'
            ];

            $this->success('微信工具类测试成功', $result);

        } catch (\Exception $e) {
            $this->error('微信工具类测试失败：' . $e->getMessage());
        }
    }
}

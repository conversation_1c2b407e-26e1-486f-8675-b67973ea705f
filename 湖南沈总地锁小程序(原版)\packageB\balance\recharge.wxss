/* packageB/balance/recharge.wxss */
.container {
  background: #F8FAF9;
  min-height: 100vh;
  padding: 30rpx;
}

/* 充值金额输入 */
.amount-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
}

.amount-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #E5E5E5;
  padding-bottom: 20rpx;
}

.currency-symbol {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
  margin-right: 10rpx;
}

.amount-input {
  flex: 1;
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
}

/* 快捷金额选择 */
.quick-amount-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
}

.quick-amount-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.quick-amount-item {
  background: #F8FAF9;
  border: 2rpx solid #E5E5E5;
  border-radius: 12rpx;
  padding: 24rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.quick-amount-item.selected {
  background: #FF6B9D;
  border-color: #FF6B9D;
  color: #fff;
}

/* 充值说明 */
.recharge-tips {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 60rpx;
}

.tips-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.tips-content {
  
}

.tip-item {
  font-size: 28rpx;
  color: #666;
  line-height: 40rpx;
  margin-bottom: 16rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 确认充值按钮 */
.recharge-btn-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.recharge-btn {
  width: 100%;
  background: linear-gradient(90deg, #FF6B9D 0%, #FF8E8E 100%);
  border: none;
  border-radius: 50rpx;
  font-size: 34rpx;
  line-height: 88rpx;
  color: #fff;
  font-weight: bold;
  box-shadow: 0px 8px 20px rgba(255, 107, 157, 0.3);
}

.recharge-btn.loading {
  background: #ccc;
  box-shadow: none;
}

.recharge-btn::after {
  border: none;
}

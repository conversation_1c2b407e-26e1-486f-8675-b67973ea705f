// packageA/lockset/lockset.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		ordercode: '', // 订单编号
		equipment:{},// 设备信息
		status_show:false,
		flags:true,
		pollCount: 0, // 轮询次数
		maxPollCount: 30, // 最大轮询次数
		pollInterval: 2000, // 轮询间隔(毫秒)
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.setData({
			ordercode: options.ordercode,
			id: options.id,
		})
		this.getequipment()
	},

	// 获取设备信息
	getequipment() {
		let that = this
		let params = {
			id: that.data.id
		}
		app.post('Ajax/equipmentInfo', params).then(res => {
			const {	code,	data,msg} = res //接口数据
			if (code == 1) {
				that.setData({
					equipment: data.data,
				})
				// that.link()
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {
		})
	},

	// 6-结束订单
	orderUpdateStatus() {
		let that = this
		let params = {
			ordercode: that.data.ordercode
		}
		app.post('Order/orderUpdateStatus', params).then(res => {
			const {	code,	data,msg} = res //接口数据
			if (code == 1) {
				// 开锁指令发送成功，开始轮询查询订单状态
				that.setData({
					pollCount: 0
				})
				that.pollOrderStatus();
			} else {
				that.setData({
					flags:true,
					status_show:false
				})
				wx.hideLoading();
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {
			that.setData({
				flags:true,
				status_show:false
			})
			wx.hideLoading();
			wx.showToast({
				title: '网络错误，请重试',
				icon: 'none',
				duration: 2000
			})
		})
	},

	// 轮询查询订单状态
	pollOrderStatus() {
		let that = this
		let params = {
			ordercode: that.data.ordercode
		}

		// 增加轮询次数
		that.setData({
			pollCount: that.data.pollCount + 1
		})

		console.log(`开始第${that.data.pollCount}次轮询查询订单状态`)

		app.post('Order/get_order_info', params).then(res => {
			const { code, data, msg } = res
			if (code == 1) {
				// 查询成功，订单状态已更新，跳转到订单详情页
				console.log('轮询成功，订单状态已更新')
				wx.hideLoading();
				that.setData({
					status_show: false,
					flags: true
				})
				wx.redirectTo({
					url: '/packageA/rental/detail?ordercode=' + that.data.ordercode,
				})
			} else {
				// 订单还在开锁中，继续轮询
				if (that.data.pollCount < that.data.maxPollCount) {
					console.log(`订单开锁中，${that.data.pollInterval/1000}秒后进行第${that.data.pollCount + 1}次查询`)
					setTimeout(() => {
						that.pollOrderStatus()
					}, that.data.pollInterval)
				} else {
					// 达到最大轮询次数，提示用户
					console.log('达到最大轮询次数，停止轮询')
					wx.hideLoading();
					that.setData({
						status_show: false,
						flags: true
					})
					wx.showModal({
						title: '提示',
						content: '开锁时间较长，请稍后手动刷新查看订单状态，或联系客服处理',
						showCancel: true,
						cancelText: '稍后查看',
						confirmText: '重试开锁',
						success(res) {
							if (res.confirm) {
								// 用户选择重试
								that.handleKai()
							}
						}
					})
				}
			}
		}).catch((err) => {
			console.log('轮询请求失败:', err)
			// 网络错误，继续重试
			if (that.data.pollCount < that.data.maxPollCount) {
				console.log(`网络错误，${that.data.pollInterval/1000}秒后重试`)
				setTimeout(() => {
					that.pollOrderStatus()
				}, that.data.pollInterval)
			} else {
				// 达到最大轮询次数
				wx.hideLoading();
				that.setData({
					status_show: false,
					flags: true
				})
				wx.showModal({
					title: '网络错误',
					content: '网络连接异常，请检查网络后重试',
					showCancel: true,
					cancelText: '稍后重试',
					confirmText: '立即重试',
					success(res) {
						if (res.confirm) {
							that.handleKai()
						}
					}
				})
			}
		})
	},

	// 归还开锁
	handleKai() {
		let that = this
		wx.showLoading({
			title: '正在开锁...',
			mask: true,
		})
		that.setData({
			status_show:true,
			flags:false,
		})
		that.orderUpdateStatus();
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
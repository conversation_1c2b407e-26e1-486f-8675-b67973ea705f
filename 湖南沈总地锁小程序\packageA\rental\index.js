// packageA/rental/index.js
const link = require('../../js/bluetooth/link.js');
const agreement = require('../../js/bluetooth/agreement.js');
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		statusBar: app.globalData.statusBar,
		customBar: app.globalData.customBar,
		custom: app.globalData.custom,

		title: '', // 标题
		type: '', // 1扫码租借 2车位归还
		id: '', // 设备ID
		order_id: '', // 订单ID
		ordercode: '', // 订单编号
		equipment: '', // 设备信息

		deviceName: '', // 连接蓝牙名称
		deviceId: '', // 连接蓝牙mac
		services: [], // 蓝牙所有服务
		servicesUuid: '', // 蓝牙服务uuid
		characterWriteUuid: '', // 发送指令特征值uuid
		characterNotifyUuid: '', // 接收消息特征是

		isUnLock: 1, // 1默认 2开锁 3已开

		isLock: 1, // 1默认 2关锁 3已关

		isShow: true, // 协议弹层
		checkbox: false, // 选框状态
		content: '', // 协议

		upLock: false, // 升锁

	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		if (options.type == 1) {
			this.setData({
				title: '扫码租借'
			})
		} else {
			this.setData({
				title: '扫码归还',
				ordercode: options.ordercode
			})
		}
		this.setData({
			type: options.type,
			id: options.id,
		})
		this.getequipment()
	},

	

	// 获取设备信息
	getequipment() {
		let that = this
		let params = {
			id: that.data.id
		}
		app.post('Ajax/equipmentInfo', params).then(res => {
			const {	code,	data,msg} = res //接口数据
			if (code == 1) {
				that.setData({
					equipment: data.data,
					deviceName: data.data.lyname,
					deviceId: data.data.mac,
				})
				// that.link()
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {
		})
	},

	//连接蓝牙
	link() {
		var that = this;
		wx.getSetting({
			success(res) {
				console.log(res.authSetting['scope.bluetooth'])
				if (res.authSetting['scope.bluetooth'] == undefined) {
					wx.showLoading({
						title: '蓝牙连接中...'
					})
					link.openBluetooth(that)
				} else if (res.authSetting['scope.bluetooth'] == true) {
					wx.showLoading({
						title: '蓝牙连接中...'
					})
					link.openBluetooth(that)
				} else if (res.authSetting['scope.bluetooth'] == false) {
					wx.showModal({
						title: '提示',
						content: '未开启授权蓝牙，是否开启蓝牙权限？',
						confirmText: '开启',
						cancelText: '跳过',
						success(res) {
							if (res.confirm) {
								wx.openSetting({
									success(res) {
										that.link()
									},
								})
							} else if (res.cancel) {
								// 用户选择跳过蓝牙连接，显示提示信息
								wx.showToast({
									title: '已跳过蓝牙连接，部分功能可能受限',
									icon: 'none',
									duration: 2000
								})
							}
						}
					})
				}
			}
		})
	},


	// 3-租借开锁
	getunLock() {
		let that = this
		let params = {
			id: that.data.id
		}
		app.post('Order/index', params).then(res => {
			const {	code,data,msg} = res //接口数据
			if (code == 1) {
				wx.showLoading({
					title: 'ceshi正在开锁...',
					mask: true,
				})
				that.setData({
					order_id: data,
					isUnLock: 2,
				})
				that.Order();
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 4-订单开始使用
	Order() {
		console.log('订单开始')
		let that = this
		let params = {
			order_id: that.data.order_id
		}
		app.post('Order/orderUse', params).then(res => {
			console.log('old旧的');
			const {code,data,	msg} = res //接口数据
			if (code == 1) {
				that.setData({
					isUnLock: 3
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 返回首页
	goHome() {
		let that = this
		wx.showModal({
			title: '提示',
			content: '车辆推入车位再点击确认，确定后将关闭蓝牙回到首页！',
			success(res) {
				if (res.confirm) {
					wx.closeBLEConnection({
						deviceId: that.data.deviceId,
						success: (res) => {
							console.log('蓝牙断开成功1', res)
							wx.closeBluetoothAdapter({
								success(resp) {
									console.log('蓝牙断开成功2', resp)
									wx.setStorageSync('deviceId', '')
									that.setData({
										deviceName: '',
										deviceId: '',
										services: [],
										servicesUuid: '',
										characterWriteUuid: '',
										characterNotifyUuid: '',
									})
									wx.switchTab({
										url: '/pages/index/index',
									})
								}
							})
						},
						fail: function (res) {
							console.log('蓝牙断开失败1', res)
							wx.closeBluetoothAdapter({
								success(resp) {
									console.log('蓝牙断开成功2', resp)
									wx.setStorageSync('deviceId', '')
									that.setData({
										deviceName: '',
										deviceId: '',
										services: [],
										servicesUuid: '',
										characterWriteUuid: '',
										characterNotifyUuid: '',
									})
									wx.switchTab({
										url: '/pages/index/index',
									})
								}
							})
						}
					})
				} else if (res.cancel) {
					console.log('用户点击取消')
				}
			}
		})
	},

	// 协议选择
	getcheck() {
		this.setData({
			checkbox: !this.data.checkbox
		});
	},

	// 获取协议
	getagreement() {
		let that = this
		let params = {
			id: 4
		}
		app.post('Ajax/getArticleInfo', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					content: data.content,
					isShow: !that.data.isShow,
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 关闭弹层
	getclose() {
		this.setData({
			isShow: !this.data.isShow,
		})
	},

	// 归还开锁
	getLock() {
		let that = this
		if (that.data.checkbox == false) {
			wx.showToast({
				title: '无法开锁，请勾选取车协议',
				icon: 'none',
				duration: 2000
			})
			return
		}
		wx.showLoading({
			title: '正在开锁...',
			mask: true,
		})
		that.setData({
			isUnLock: 2,
		})
		that.orderUpdateStatus();
		return
		agreement.sendOut(that, 1, function (e) {
			//数据发送成功

		}, function (e) {
			//数据发送失败
			that.setData({
				isUnLock: 1
			})
			wx.hideLoading();
			wx.showToast({
				title: '开锁失败',
				icon: 'none',
				duration: 2000
			});
		});
	},

	// 关锁
	getUpLock() {
		let that = this
		wx.showModal({
			title: '提示',
			content: '车辆推出车位再点击确认，确定后将关闭蓝牙跳转到支付页面！',
			success(res) {
				if (res.confirm) {
					if (that.data.checkbox == false) {
						wx.showToast({
							title: '无法关锁，请勾选取车协议',
							icon: 'none',
							duration: 2000
						})
						return
					}
					wx.showLoading({
						title: '正在关锁...',
						mask: true,
					})
					that.setData({
						isLock: 2,
					})
					agreement.sendOut(that, 2, function (e) {
						//数据发送成功

					}, function (e) {
						//数据发送失败
						that.setData({
							isLock: 1
						})
						wx.hideLoading();
						wx.showToast({
							title: '关锁失败',
							icon: 'none',
							duration: 2000
						});
					});
				} else if (res.cancel) {
					console.log('用户点击取消')
				}
			}
		})
	},

	// 6-结束订单
	orderUpdateStatus() {
		let that = this
		let params = {
			ordercode: that.data.ordercode
		}
		app.post('Order/orderUpdateStatus', params).then(res => {
			const {	code,	data,msg} = res //接口数据
			if (code == 1) {
				// that.setData({
				// 	upLock: true
				// })
				wx.navigateTo({
					url: '/packageA/rental/detail?ordercode='+that.data.ordercode,
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 关锁关闭蓝牙
	getcloseBLEConnection() {
		let that = this
		console.log('订单结束')
		wx.closeBLEConnection({
			deviceId: that.data.deviceId,
			success: (res) => {
				console.log('蓝牙断开成功1', res)
				wx.closeBluetoothAdapter({
					success(resp) {
						console.log('蓝牙断开成功2', resp)
						wx.setStorageSync('deviceId', '')
						that.setData({
							deviceName: '',
							deviceId: '',
							services: [],
							servicesUuid: '',
							characterWriteUuid: '',
							characterNotifyUuid: '',
						})
						wx.reLaunch({
							url: '/packageA/rental/detail?ordercode=' + that.data.ordercode,
						})
					}
				})
			},
			fail: function (res) {
				console.log('蓝牙断开失败1', res)
				wx.closeBluetoothAdapter({
					success(resp) {
						console.log('蓝牙断开成功2', resp)
						wx.setStorageSync('deviceId', '')
						that.setData({
							deviceName: '',
							deviceId: '',
							services: [],
							servicesUuid: '',
							characterWriteUuid: '',
							characterNotifyUuid: '',
						})
						wx.navigateTo({
							url: '/packageA/rental/detail?ordercode=' + that.data.ordercode,
						})
					}
				})
			}
		})
	},

	// 关闭蓝牙
	getcloseBLE() {
		let that = this
		wx.closeBLEConnection({
			deviceId: that.data.deviceId,
			success: (res) => {
				console.log('蓝牙断开成功1', res)
				wx.closeBluetoothAdapter({
					success(resp) {
						console.log('蓝牙断开成功2', resp)
						wx.setStorageSync('deviceId', '')
						that.setData({
							deviceName: '',
							deviceId: '',
							services: [],
							servicesUuid: '',
							characterWriteUuid: '',
							characterNotifyUuid: '',
						})
					}
				})
			},
			fail: function (res) {
				console.log('蓝牙断开失败1', res)
				wx.closeBluetoothAdapter({
					success(resp) {
						console.log('蓝牙断开成功2', resp)
						wx.setStorageSync('deviceId', '')
						that.setData({
							deviceName: '',
							deviceId: '',
							services: [],
							servicesUuid: '',
							characterWriteUuid: '',
							characterNotifyUuid: '',
						})
					}
				})
			}
		})
	},

	getBack(){
		this.getcloseBLE()
		wx.navigateBack({
			delta: 1,
		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {
		// let that = this
		// wx.closeBLEConnection({
		// 	deviceId: that.data.deviceId,
		// 	success: (res) => {
		// 		console.log('蓝牙断开成功1', res)
		// 		wx.closeBluetoothAdapter({
		// 			success(resp) {
		// 				console.log('蓝牙断开成功2', resp)
		// 				wx.setStorageSync('deviceId', '')
		// 				that.setData({
		// 					deviceName: '',
		// 					deviceId: '',
		// 					services: [],
		// 					servicesUuid: '',
		// 					characterWriteUuid: '',
		// 					characterNotifyUuid: '',
		// 				})
		// 			}
		// 		})
		// 	}, fail: function (res) {
		// 		console.log('蓝牙断开失败1', res)
		// 		wx.closeBluetoothAdapter({
		// 			success(resp) {
		// 				console.log('蓝牙断开成功2', resp)
		// 				wx.setStorageSync('deviceId', '')
		// 				that.setData({
		// 					deviceName: '',
		// 					deviceId: '',
		// 					services: [],
		// 					servicesUuid: '',
		// 					characterWriteUuid: '',
		// 					characterNotifyUuid: '',
		// 				})
		// 			}
		// 		})
		// 	}
		// })
	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
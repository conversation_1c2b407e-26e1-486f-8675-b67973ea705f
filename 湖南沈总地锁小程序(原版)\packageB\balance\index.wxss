/* packageB/balance/index.wxss */
.container {
  background: #F8FAF9;
  min-height: 100vh;
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(135deg, #FF6B9D 0%, #FF8E8E 100%);
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  color: #fff;
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
}

.balance-rule {
  font-size: 24rpx;
  opacity: 0.8;
  padding: 8rpx 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.balance-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  gap: 8rpx;
}

.recharge-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn image {
  width: 32rpx;
  height: 32rpx;
}

/* 余额明细 */
.balance-detail {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.detail-list {
  
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F5F5F5;
}

.detail-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F8FAF9;
}

.item-icon image {
  width: 40rpx;
  height: 40rpx;
}

.item-info {
  
}

.item-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.item-time {
  font-size: 24rpx;
  color: #999;
}

.item-right {
  
}

.item-amount {
  font-size: 32rpx;
  font-weight: bold;
}

.item-amount.positive {
  color: #FF6B9D;
}

.item-amount.negative {
  color: #FF8E53;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999;
}

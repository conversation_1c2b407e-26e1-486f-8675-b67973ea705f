// packageB/setrecords/index.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		states:[
			{
				state: '1',
				title: '使用中',
			},
			{
				state: '2',
				title: '已过期',
			},
		],// 状态

		state: '1',// 选中状态

		list:[],// 套餐记录
		loading: false,//是否加载中
		loadend: false,//是否加载完毕
		loadTitle: '加载更多',//提示语
		page: 1,
		limit: 15,

	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.getList();
	},

	// 状态切换
	getState(e){
		this.setData({
			state: e.currentTarget.dataset.state,
			list: [],
			loading: false,//是否加载中
			loadend: false,//是否加载完毕
			page: 1,
		});
		this.getList();
	},

	// 套餐记录
	getList(){
		var that = this;
		if (that.data.loadend) return;
		if (that.data.loading) return;
		let params = {
			state: that.data.state,
			page: that.data.page,
			limit: that.data.limit,
			token: wx.getStorageSync("token"),
		}
		app.post('Setmeal/mylists', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				if (that.data.page == 1) {
					that.setData({
						list: [],
					});
				}
				// 数据赋值
				var list = data.list || [];
				// 合并数组
				that.data.list = app.SplitArray(list, that.data.list);
				var loadend = data.nextpage;
				that.setData({
					list: that.data.list,
					loadend: loadend,
					loading: false,
					loadTitle: loadend ? "加载更多" : '到底啦...',
					page: that.data.page + 1,
				});
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {
		let that = this
		that.setData({
			list: [],
			stopPull: true,

			loading: false,//是否加载中
			loadend: false,//是否加载完毕
			page: 1,
		});
		that.getList();
		wx.stopPullDownRefresh();
	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {
		this.getList();
	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
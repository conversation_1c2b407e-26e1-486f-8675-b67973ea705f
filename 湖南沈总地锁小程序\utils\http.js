/**
 *  这是一个小程序封装的的全局请求方式
 *  Created by gaobo on 2021/03/06
 */
var util = require('./util.js') // 公用插件方法
// 地址
const url = {
  // dev: 'https://disuo.zgxiaochengxu.com/api/',  // 测试域名
  dev: 'https://xcx.gjgxds.com/api/',  // 正式域名
};
const apiUrl = url.dev;

// token 
let token = ""
// 公共参数 


//  api      请求方式/路由
//  params   参数
const httpTokenRequest = (opts, params) => {
  if (!params) params = {}
  // 公共参数
  // if(!params.token){
  // 	params.token =  token = cache.fetchCache(cache.TOKEN) || ""
  // }
  if (params.token) {
    token = params.token
  } else {
    token = wx.getStorageSync("token") || ""
  }
  let promise = new Promise(function (resolve, reject) {
    // console.log(token)
    wx.request({
      url: apiUrl + opts.url, //仅为示例，并非真实的接口地址
      data: params, // 参数
      method: opts.method, // 请求方式
      header: {
        'token': token,
        'Content-Type': 'application/json'
      },
      success(res) {
        if (res.data.code == 401) {
          wx.setStorageSync('token', ''); // 将token存入缓存
          wx.showToast({
            title: '登录失效，请重新登录',
            icon: 'none',
            duration: 3000
          })
        } else {
          resolve(res.data)
        }
      },
      fail(res) {
        reject(res.data)
      },
      complete(res) {

      }
    })

  })
  return promise

}


// POST请求
const optsPost = {
  method: "POST",
  url: ''
}
// GET请求
const optsGet = {
  method: "GET",
  url: ''
}
// 请求方式
// post
let post = (url, data) => {
  try {
    optsPost.url = url
    return httpTokenRequest(optsPost, data)
  } catch (e) {
    console.log(e)
    //TODO handle the exception
  }
}
// get
let get = (url, data) => {
  try {
    optsGet.url = url
    return httpTokenRequest(optsGet, data)
  } catch (e) {
    console.log(e)
    //TODO handle the exception
  }
}
module.exports = {
  httpTokenRequest,
  apiUrl,
  /**
   * 写需求请求的接口
   */
  // 接口请求
  post,
  get,
}
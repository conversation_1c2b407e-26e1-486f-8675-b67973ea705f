/* packageB/setrecords/index.wxss */
.container{
	background: #F7F7F7;
}
.setrecord{
	padding-top: 120rpx;
}
.setrecord .nav{
	position: fixed;
	width: 100%;
	left: 0;
	top: 0;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-around;
	background: #fff;
	border-top: 1rpx solid #F7F7F7;
}
.setrecord .nav view{
	font-size: 30rpx;
	line-height: 98rpx;
	color: #333;
	position: relative;
}
.setrecord .nav view.active{
	color: #2DA7F6;
	font-weight: bold;
}
.setrecord .nav view.active::after{
	content: '';
	position: absolute;
	width: 100%;
	height: 3rpx;
	background: #2DA7F6;
	left: 0;
	bottom: 0;
}
.setrecord .list{
	background: #fff;
	padding: 0 30rpx;
}
.setrecord .list .nr{
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	border-top: 1px solid #EFEFEF;
	padding: 25rpx 0;
}
.setrecord .list .nr:first-child{
	border-top: none;
}
.setrecord .list .nr .xx view{
	font-size: 26rpx;
	line-height: 36rpx;
	color: #333;
}
.setrecord .list .nr .xx text{
	font-size: 20rpx;
	line-height: 30rpx;
	color: #999;
}
.setrecord .list .nr .price{
	font-size: 32rpx;
	line-height: 42rpx;
	color: #333;
	font-weight: bold;
}
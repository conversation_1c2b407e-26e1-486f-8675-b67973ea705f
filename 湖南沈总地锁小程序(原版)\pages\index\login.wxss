/* pages/index/login.wxss */
.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}
.name{
  display: flex;
  flex-wrap: wrap;
  padding: 0 30rpx;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}
.name view{
  font-size: 30rpx;
  line-height: 90rpx;
  color: #333;
  font-weight: bold;
}
.name input{
  font-size: 30rpx;
  line-height: 90rpx;
  color: #999;
  height: 90rpx;
}
.btn{
  width: 80%;
  margin: 250rpx auto 0;
  text-align: center;
  font-size: 34rpx;
  line-height: 88rpx;
  color: #fff;
	background: linear-gradient(90deg, #2298F9 0%, #57B2FC 47%, #57B2FC 47%, #57B2FC 47%, #57B2FC 47%, #57B2FC 47%, #94CFFF 100%);
	border-radius: 80rpx;
}
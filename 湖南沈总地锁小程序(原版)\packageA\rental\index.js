// packageA/rental/index.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		statusBar: app.globalData.statusBar,
		customBar: app.globalData.customBar,
		custom: app.globalData.custom,

		title: '', // 标题
		type: '', // 1扫码租借 2车位归还
		id: '', // 设备ID
		order_id: '', // 订单ID
		ordercode: '', // 订单编号
		equipment: '', // 设备信息



		isUnLock: 1, // 1默认 2开锁 3已开

		isLock: 1, // 1默认 2关锁 3已关

		isShow: true, // 协议弹层
		checkbox: false, // 选框状态
		content: '', // 协议

		upLock: false, // 升锁

	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		if (options.type == 1) {
			this.setData({
				title: '扫码租借'
			})
		} else {
			this.setData({
				title: '扫码归还',
				ordercode: options.ordercode
			})
		}
		this.setData({
			type: options.type,
			id: options.id,
		})
		this.getequipment()
	},

	// 获取设备信息
	getequipment() {
		let that = this
		let params = {
			id: that.data.id
		}
		app.post('Ajax/equipmentInfo', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					equipment: data.data,
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},




	// 租借开锁
	getunLock() {
		let that = this
		let params = {
			id: that.data.id
		}
		app.post('Order/index', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				wx.showLoading({
					title: '正在锁车...',
					mask: true,
				})
				that.setData({
					order_id: data,
					isUnLock: 2,
				})
				// 开始轮询查询订单状态
				that.startPollingOrderStatus(data);
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {
			wx.hideLoading();
			wx.showToast({
				title: '网络错误',
				icon: 'none',
				duration: 2000
			})
		})
	},

	// 轮询查询订单状态
	startPollingOrderStatus(orderId) {
		let that = this;
		let pollCount = 0;
		const maxPollCount = 30; // 最多轮询30次（约60秒）

		const pollTimer = setInterval(() => {
			pollCount++;

			// 调用orderUse接口查询订单状态
			app.post('Order/orderUse', { order_id: orderId }).then(res => {
				const { code, data, msg } = res;

				if (code == 1) {
					// 查询到订单状态为use_status=2，表示锁车成功
					clearInterval(pollTimer);
					wx.hideLoading();
					that.setData({
						isUnLock: 3,
					})
					wx.showToast({
						title: '锁车成功',
						icon: 'success',
						duration: 2000
					});
				} else if (pollCount >= maxPollCount) {
					// 超时停止轮询
					clearInterval(pollTimer);
					wx.hideLoading();
					that.setData({
						isUnLock: 1
					})
					wx.showToast({
						title: '锁车超时，请重试',
						icon: 'none',
						duration: 2000
					});
				}
			}).catch((err) => {
				if (pollCount >= maxPollCount) {
					clearInterval(pollTimer);
					wx.hideLoading();
					that.setData({
						isUnLock: 1
					})
					wx.showToast({
						title: '网络错误，请重试',
						icon: 'none',
						duration: 2000
					});
				}
			});
		}, 2000); // 每2秒轮询一次
	},

	// 订单开始使用
	Order() {
		console.log('订单开始')
		let that = this
		let params = {
			order_id: that.data.order_id
		}
		app.post('Order/orderUse', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					isUnLock: 3
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 返回首页
	goHome() {
		let that = this
		wx.showModal({
			title: '提示',
			content: '车辆推入车位再点击确认，确定后将回到首页！',
			success(res) {
				if (res.confirm) {
					wx.switchTab({
						url: '/pages/index/index',
					})
				} else if (res.cancel) {
					console.log('用户点击取消')
				}
			}
		})
	},

	// 协议选择
	getcheck() {
		this.setData({
			checkbox: !this.data.checkbox
		});
	},

	// 获取协议
	getagreement() {
		let that = this
		let params = {
			id: 4
		}
		app.post('Ajax/getArticleInfo', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					content: data.content,
					isShow: !that.data.isShow,
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 关闭弹层
	getclose() {
		this.setData({
			isShow: !this.data.isShow,
		})
	},

	// 归还开锁
	getLock() {
		let that = this
		if (that.data.checkbox == false) {
			wx.showToast({
				title: '无法开锁，请勾选取车协议',
				icon: 'none',
				duration: 2000
			})
			return
		}
		wx.showLoading({
			title: '正在开锁...',
			mask: true,
		})
		that.setData({
			isUnLock: 2,
		})

		// 调用orderUpdateStatus接口
		that.orderUpdateStatus();
	},

	// 关锁
	getUpLock() {
		let that = this
		wx.showModal({
			title: '提示',
			content: '车辆推出车位再点击确认，确定后将跳转到支付页面！',
			success(res) {
				if (res.confirm) {
					if (that.data.checkbox == false) {
						wx.showToast({
							title: '无法关锁，请勾选取车协议',
							icon: 'none',
							duration: 2000
						})
						return
					}
					// 直接跳转到支付页面
					wx.reLaunch({
						url: '/packageA/rental/detail?ordercode=' + that.data.ordercode,
					})
				} else if (res.cancel) {
					console.log('用户点击取消')
				}
			}
		})
	},

	// 结束订单
	orderUpdateStatus() {
		let that = this
		let params = {
			ordercode: that.data.ordercode
		}
		app.post('Order/orderUpdateStatus', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			wx.hideLoading(); // 隐藏loading
			if (code == 1) {
				that.setData({
					isUnLock: 3, // 设置为开锁成功状态
					upLock: true
				})
				wx.showToast({
					title: '开锁成功',
					icon: 'success',
					duration: 2000
				});
			} else {
				that.setData({
					isUnLock: 1 // 恢复初始状态
				})
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {
			wx.hideLoading(); // 隐藏loading
			that.setData({
				isUnLock: 1 // 恢复初始状态
			})
			wx.showToast({
				title: '网络错误',
				icon: 'none',
				duration: 2000
			})
		})
	},

	// 订单结束跳转
	getcloseBLEConnection() {
		let that = this
		console.log('订单结束')
		wx.reLaunch({
			url: '/packageA/rental/detail?ordercode=' + that.data.ordercode,
		})
	},

	getBack(){
		wx.navigateBack({
			delta: 1,
		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
// packageB/order/details.js
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		id: '',// 订单ID
		infor: '',// 订单详情
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.setData({
			id: options.id
		})
		this.getInfor()
	},

	// 订单详情
	getInfor(){
		var that = this;
		let params = {
			status: that.data.type,
		}
		app.post('Order/getMyOrder', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
<!--packageB/setrecords/index.wxml-->
<view class="container">
	<view class="setrecord">
		<view class="nav">
			<view class="{{state == item.state?'active':''}}" wx:for="{{states}}" data-state="{{item.state}}" bindtap="getState">{{item.title}}</view>
		</view>
		<view class="list">
			<view class="nr" wx:for="{{list}}" wx:if="{{list.length > 0}}">
				<view class="xx">
					<view>{{item.set_name}}</view>
					<view>物业：{{item.hos_name}}</view>
					<text>有效期：({{item.effective_start}}至{{item.effective_end}})</text>
				</view>
				<view class="price">￥{{item.pay_money}}</view>
			</view>

			<view class='loading' wx:if="{{list.length > 0}}">{{loadTitle}}</view>

			<view class="zwsj" wx:if="{{list.length == 0}}">
				<image src="/image/icon_wsj.png" mode="widthFix"></image>
    		<view>暂无购买套餐记录</view>
			</view>

		</view>
	</view>
</view>
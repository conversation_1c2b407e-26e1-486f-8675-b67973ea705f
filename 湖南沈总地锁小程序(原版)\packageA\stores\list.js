// packageA/index/stores/list.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		list:[],// 门店列表
		loading: false,//是否加载中
		loadend: false,//是否加载完毕
		loadTitle: '加载更多',//提示语
		page: 1,
		limit: 15,

		search: '',// 搜索信息
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.getList()
	},

	// 获取搜索信息
	getSearch(e){
		this.setData({
			search: e.detail.value,
			list: [],
			loading: false,//是否加载中
			loadend: false,//是否加载完毕
			page: 1,
		})
		this.getList()
	},

	// 门店列表
	getList(){
		var that = this;
		if (that.data.loadend) return;
		if (that.data.loading) return;
		let params = {
			latitude: wx.getStorageSync("lat"),
			longitude: wx.getStorageSync("lon"),
			name: that.data.search,
			page: that.data.page,
			limit: that.data.limit,
		}
		app.post('Hospital/lists', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				if (that.data.page == 1) {
					that.setData({
						list: [],
					});
				}
				// 数据赋值
				var list = data.list || [];
				// 合并数组
				that.data.list = app.SplitArray(list, that.data.list);
				var loadend = data.nextpage;
				that.setData({
					list: that.data.list,
					loadend: loadend,
					loading: false,
					loadTitle: loadend ? "加载更多" : '到底啦...',
					page: that.data.page + 1,
				});
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 导航
	goLocation(e){
		var latitude = e.currentTarget.dataset.lat;
		var longitude = e.currentTarget.dataset.lon;
		var name = e.currentTarget.dataset.name;
		console.log
		var addr = e.currentTarget.dataset.addr;
		console.log
		wx.getLocation({
				type: 'wgs84',
				success: function (res) {
						wx.openLocation({//​使用微信内置地图查看位置。
								latitude: Number(latitude),//要去的纬度-地址
								longitude: Number(longitude),//要去的经度-地址
								name: name,
								address: addr,
								scale: 18,
						})
				}
		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {
		let that = this
		that.setData({
			list: [],
			stopPull: true,

			loading: false,//是否加载中
			loadend: false,//是否加载完毕
			page: 1,
		});
		that.getList();
		wx.stopPullDownRefresh();
	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {
		this.getList();
	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
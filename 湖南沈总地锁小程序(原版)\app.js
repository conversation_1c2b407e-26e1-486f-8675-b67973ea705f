//app.js

const util = require('utils/util.js');
const http = require('utils/http.js');

App({


  onLaunch: function () {
    wx.getSystemInfo({
      success: e => {
				wx.setStorageSync('SDKVersion', e.SDKVersion);
				this.globalData.safeBottom = e.screenHeight - e.safeArea.bottom
        this.globalData.screenHeight = e.screenHeight;// 屏幕高度
        this.globalData.bottom = e.safeArea.bottom;// 底部距顶部距离
        this.globalData.home = e.screenHeight - e.safeArea.bottom;// 距离差 0非全面屏
        this.globalData.statusBar = e.statusBarHeight; //状态栏高度
        let custom = wx.getMenuButtonBoundingClientRect();//菜单按钮
        this.globalData.custom = custom;
        this.globalData.customBar = custom.bottom + custom.top - e.statusBarHeight;
				//计算得到定义的状态栏高度
      }
    })
	},
	
	onHide() {
		// if(wx.getStorageSync("deviceId")){
		// 	wx.closeBLEConnection({
		// 		deviceId: wx.getStorageSync("deviceId"),
		// 		success: (res) => {
		// 			console.log('蓝牙断开成功1', res)
		// 			wx.closeBluetoothAdapter({
		// 				success(resp) {
		// 					wx.setStorageSync('deviceId', '')
		// 					console.log('蓝牙断开成功2', resp)
		// 				}
		// 			})
		// 		}, fail: function (res) {
		// 			console.log('蓝牙断开失败1', res)
		// 			wx.closeBluetoothAdapter({
		// 				success(resp) {
		// 					wx.setStorageSync('deviceId', '')
		// 					console.log('蓝牙断开成功2', resp)
		// 				}
		// 			})
		// 		}
		// 	})
		// 	wx.switchTab({
		// 		url: '/pages/index/index',
		// 	})
		// }
	},

  // api
  apiUrl() {
    return http.apiUrl
  },

  // post请求  路由/参数
  post(url, data) {
    return http.post(url, data)
  },
  // get 请求    路由/参数
  get(url, data) {
    return http.get(url, data)
  },


  globalData: {

  },

  /**
   * 公共方法：获取微信手机号（新版方式）
   * 统一处理手机号获取逻辑，避免代码重复
   * @param {Object} e 微信回调事件对象
   * @param {Function} successCallback 成功回调函数，参数为手机号
   * @param {Function} failCallback 失败回调函数，参数为错误信息
   */
  getWechatPhoneNumber: function(e, successCallback, failCallback) {
    console.log('手机号授权回调事件：', e);

    // 检查是否获取到code
    if (!e.detail.code) {
      const errorMsg = '获取手机号失败，请重试';
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      });
      if (typeof failCallback === 'function') {
        failCallback(errorMsg);
      }
      return;
    }

    // 检查用户是否已登录
    if (!wx.getStorageSync("token")) {
      const errorMsg = '请先登录';
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      });
      if (typeof failCallback === 'function') {
        failCallback(errorMsg);
      }
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '获取手机号中...',
      mask: true
    });

    // 使用新版方式：传递code而不是encryptedData和iv
    let params = {
      code: e.detail.code,  // 新版方式使用动态令牌code
      token: wx.getStorageSync("token"),
    };

    // 调用后端接口
    console.log('准备调用后端接口，参数：', params);
    this.post('user/wxUpdateMobile', params).then(res => {
      wx.hideLoading();
      console.log('后端接口返回结果：', res);

      const { code, data, msg } = res;
      if (code == 1) {
        // 更新本地存储
        wx.setStorageSync('mobile', data);
        console.log('手机号获取成功，已存储：', data);

        // 显示成功提示
        wx.showToast({
          title: '手机号获取成功',
          icon: 'success',
          duration: 1500
        });

        // 执行成功回调
        if (typeof successCallback === 'function') {
          successCallback(data);
        }
      } else {
        console.log('后端返回错误：', msg);
        // 显示错误提示
        wx.showToast({
          title: msg || '获取手机号失败',
          icon: 'none',
          duration: 2000
        });

        // 执行失败回调
        if (typeof failCallback === 'function') {
          failCallback(msg || '获取手机号失败');
        }
      }
    }).catch((err) => {
      wx.hideLoading();
      console.error('网络请求失败：', err);

      const errorMsg = '网络请求失败，请重试';
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      });

      // 执行失败回调
      if (typeof failCallback === 'function') {
        failCallback(errorMsg);
      }
    });
  },

  /*
* 合并数组
* @param array list 请求返回数据
* @param array sp 原始数组
* @return array
*/
  SplitArray: function (list, sp) { return util.SplitArray(list, sp) },


  toLogin() {
    var that = this
    return new Promise(function (resolve, reject) {
      wx.login({
        success(re) {
          if (re.code) {
            wx.showModal({
              title: '提示',
              content: '是否授权登录',
              success(res) {
                if (res.confirm) {
                  wx.getUserProfile({
                    desc: '需要登录',
                    success: function (resp) {
											console.log(re)
											console.log(resp)
                      let params = {
                        code: re.code,
                        nickName: resp.userInfo.nickName,
                        avatarUrl: resp.userInfo.avatarUrl,
                        gender: resp.userInfo.gender,
                      }
                      that.post('user/wxxcxlogin', params).then(res => {
                        const {
                          code,
                          data,
                          msg
                        } = res //接口数据
                        if (code == 1) {
                          wx.setStorageSync('token', data.token); // 将token存入缓存
                          wx.setStorageSync('nickname', data.nickname);
                          wx.setStorageSync('avatar', data.avatar);
                          resolve();
                        } else {
                          reject();
                          wx.showToast({
                            title: '授权登录失败',
                            icon: 'error',
                            duration: 2000
                          })
                        }

                      }).catch((err) => {

                      })
                    }, fail(resp) {
                      if (resp.errMsg == "getUserProfile:fail auth deny") {
                        reject();
                        wx.showToast({
                          title: '拒绝授权',
                          icon: 'none',
                        })
                      }
                    }
                  })
                } else if (res.cancel) {
                  reject();
                  wx.showToast({
                    title: '登录失败！',
                    icon: 'error',
                    duration: 2000
                  })
                }
              }
            })
          } else {
            reject();
            wx.showToast({
              title: '登录失败！',
              icon: 'error',
              duration: 2000
            })
          }
        }
      })
    })
  },

})
// packageB/pay/index.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		isCheck: true,// 支付方式

		ordercode: '',// 订单编号
		money: '',// 支付金额
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.setData({
			money: options.money,
			ordercode: options.ordercode,
		})
	},

	// 支付方式
	getCheck() {
		this.setData({
			isCheck: !this.data.isCheck
		})
	},

	// 8-支付
	getPay() {
		let that = this
		if (!that.data.isCheck) {
			wx.showToast({
				title: '请选择支付方式',
				icon: 'none',
				duration: 2000
			})
			return
		}
		wx.showLoading({
			title: '支付中',
		})
		let params = {
			ordercode: that.data.ordercode,
		}
		app.post('Order/orderWeixin', params).then(res => {
			const {	code,data,msg} = res //接口数据
			if (code == 1) {
				wx.requestPayment({
					timeStamp: data.timeStamp,
					nonceStr: data.nonceStr,
					package: data.package,
					signType: data.signType,
					paySign: data.paySign,
					success() {
						wx.hideLoading()
						wx.showToast({
							title: "支付成功",
							icon: "none"
						});
						setTimeout(function () {
							wx.navigateTo({
								url: 'succeed',
							})
						}, 2000);
					},
					fail(res) {
						wx.hideLoading()
						wx.showToast({
							title: "支付失败",
							icon: "none"
						});
						setTimeout(function () {
							// wx.switchTab({
							// 	url: '/pages/index/index'
							// })
						}, 2000);
					}
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
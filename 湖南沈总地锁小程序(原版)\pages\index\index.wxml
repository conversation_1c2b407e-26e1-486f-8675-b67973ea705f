<!--pages/index/index.wxml-->
<view class="container">
	<view class="top">
		<view class="heard" style="padding-top:{{statusBar}}px;">
			<view class="location" style="line-height: {{statusBar}}px;top: {{statusBar}}px;">{{city}}</view>
			<view class="title" style="line-height: {{statusBar}}px;">首页</view>
		</view>
		<swiper autoplay='true' circular="true" interval='5000' duration='500' class='banner_bg'>
			<swiper-item class="item_image" wx:for="{{banner}}">
				<image src='{{item.ad_image}}' class='swiper_image' mode="aspectFill"></image>
			</swiper-item>
		</swiper>
	</view>
	<view class="service">
		<view class="title">
			<view>车位</view>
			<view class="b-color">服务</view>
			<text>停车一键便捷服务</text>
		</view>
		<view class="nav">
			<view class="lnav" bindtap="goStore">
				<image src="/image/nav_bj1.png" mode="widthFix"></image>
				<view class="nr">
					<view class="bq">
						<image src="/image/bq_bj.png" mode="heightFix"></image>
						<view>更加便捷</view>
					</view>
					<view class="text">附近门店</view>
					<view class="btn">立即前往 ></view>
				</view>
			</view>
			<view class="rnav">
				<view class="nr" data-type="1" bindtap="getCode">
					<image src="/image/nav_bj2.png" mode="widthFix"></image>
					<view class="xx">
						<view class="text">扫码租借</view>
						<view class="btn">立即租借 ></view>
					</view>
				</view>
				<view class="nr" data-type="2" bindtap="getCode">
					<image src="/image/nav_bj3.png" mode="widthFix"></image>
					<view class="xx">
						<view class="text">车位归还</view>
						<view class="btn o-color">立即归还 ></view>
					</view>
				</view>
			</view>
		</view>
	</view>
	<view class="package">
		<view class="nr" bindtap="goPackage">
			<view class="text">
				<view>车位套餐</view>
				<text>让您的出行更加实惠便捷</text>
			</view>
			<view class="btn">立即购买</view>
		</view>
	</view>

	<view class="tc tcmobile" wx:if="{{mobileShow}}">
    <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">一键获取获取手机号</button>
  </view>
</view>
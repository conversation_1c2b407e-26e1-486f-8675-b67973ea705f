<!--packageA/package/stores.wxml-->
<view class="container">
	<view class="search">
		<view>
			<image src="/image/icon_search.png"></image>
			<input type="text" confirm-type='search' bindconfirm='getSearch' placeholder="搜索你需要的门店信息" placeholder-style="color:#999" />
		</view>
	</view>
	<view class="list">
		<view class="nr" data-id="{{item.id}}" data-name="{{item.name}}" bindtap="getStore" wx:for="{{list}}" wx:if="{{list.length > 0}}">
			<view class="pic">
				<image src="{{item.logo_image}}" mode="aspectFill"></image>
			</view>
			<view class="text">
				<view class="name">{{item.name}}</view>
				<view class="numb">车位共: {{item.intotal}}</view>
				<view class="address">地址：{{item.addr}}</view>
			</view>
		</view>

		<view class='loading' wx:if="{{list.length > 0}}">{{loadTitle}}</view>

		<view class="zwsj" wx:if="{{list.length == 0}}">
			<image src="/image/icon_wsj.png" mode="widthFix"></image>
			<view>暂无物业</view>
		</view>
	</view>
</view>
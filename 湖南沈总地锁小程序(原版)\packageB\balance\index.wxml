<!--packageB/balance/index.wxml-->
<view class="container">
  <!-- 余额卡片 -->
  <view class="balance-card">
    <view class="balance-header">
      <view class="balance-title">账户余额（元）</view>
      <view class="balance-rule" bindtap="goRule">规则</view>
    </view>
    <view class="balance-amount">¥{{userBalance}}</view>
    <view class="balance-actions">
      <view class="action-btn recharge-btn" bindtap="goRecharge">
        <image src="/image/icon_recharge_white.png"></image>
        <text>充值</text>
      </view>
    </view>
  </view>

  <!-- 余额明细 -->
  <view class="balance-detail">
    <view class="detail-title">余额明细</view>
    
    <view class="detail-list" wx:if="{{balanceList.length > 0}}">
      <view class="detail-item" wx:for="{{balanceList}}" wx:key="id">
        <view class="item-left">
          <view class="item-icon">
            <image src="{{getTypeIcon(item.type)}}" wx:if="{{item.type == 1}}"></image>
            <image src="/image/icon_rent_orange.png" wx:else></image>
          </view>
          <view class="item-info">
            <view class="item-title">{{getTypeText(item.type)}}</view>
            <view class="item-time">{{formatTime(item.createtime)}}</view>
          </view>
        </view>
        <view class="item-right">
          <view class="item-amount {{item.money > 0 ? 'positive' : 'negative'}}">
            {{item.money > 0 ? '+' : ''}}¥{{item.money}}
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{balanceList.length == 0 && !loading}}">
      <image src="/image/empty_balance.png"></image>
      <view class="empty-text">暂无余额明细</view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && balanceList.length > 0}}">
      <text>没有更多了</text>
    </view>
  </view>
</view>

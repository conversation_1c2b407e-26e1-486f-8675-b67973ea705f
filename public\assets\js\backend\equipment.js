define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            var hospital_id = $('#c-hospital_id').val();
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'equipment/index/hospital_id/'+ hospital_id + location.search,
                    add_url: 'equipment/add',
                    edit_url: 'equipment/edit/hospital_id/'+ hospital_id,
                    del_url: 'equipment/del',
                    multi_url: 'equipment/multi',
                    table: 'equipment',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'agent.name', title: __('Agent.name')},
                        {field: 'hospital.name', title: __('Hospital.name'), operate: false},
                        {field: 'hospitaldepartments.deptname', title: __('Hospitaldepartments.deptname')},

                        {field: 'mainname', title: __('Mainname')},
                        // {field: 'hardware_type', title: __('Hardware_type'), searchList: {"1":"mqtt地锁"}, formatter: Table.api.formatter.status},
                        {field: 'is_online', title: __('Is_online'), searchList: {"1":"在线","0":"离线"}, formatter: Table.api.formatter.status},
                        {field: 'lock_status', title: __('Lock_status'), searchList: {"0":"已开锁(降下)","1":"已上锁(升起)"}, formatter: Table.api.formatter.status},
                        {field: 'car_status', title: __('Car_status'), searchList: {"0":"无车","1":"有车"}, formatter: Table.api.formatter.status},
                        {field: 'admin_status', title: __('Admin_status'), searchList: {"1":"启用","2":"禁用"}, formatter: Table.api.formatter.status},
                        {field: 'use_status', title: __('Use_status'), searchList: {"1":"空闲","2":"使用中","3":"故障"}, formatter: Table.api.formatter.status},
                        {field: 'voltage', title: __('Voltage'), operate: false},
                        {field: 'signal_strength', title: __('Signal_strength'), operate: false},
                        {field: 'last_status_updatetime', title: __('Last_status_updatetime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });
            
            // 添加点击事件
            $(document).on('click', '.btn-myadd', function () {
                var hospital_id = $('#c-hospital_id').val();
                var options = {
                    shadeClose: false,
                    shade: [0.3, '#393D49'],
                    callback: function (data) {
                       if(data.status == 1){
                           Toastr.success("成功");
                       }else{
                           Toastr.success("失败");
                       }
                    }
                }
                Fast.api.open('Equipment/add/hospital_id/'+ hospital_id,'添加',options);
            });
            
            // 编辑点击事件
            $(document).on('click', '.btn-myedit', function () {
                var hospital_id = $('#c-hospital_id').val();console.log(hospital_id);return;
                var options = {
                    shadeClose: false,
                    shade: [0.3, '#393D49'],
                    callback: function (data) {
                       if(data.status == 1){
                           Toastr.success("成功");
                       }else{
                           Toastr.success("失败");
                       }
                    }
                }
                Fast.api.open('Equipment/edit/hospital_id/'+ hospital_id,'添加',options);
            });
            
            //设备详情点击时间
            $(document).on('click', '.btn-info', function () {
                var dataArr = $('#table').bootstrapTable('getAllSelections');
                if(dataArr.length == 0){alert('请选择一个主设备');return false;}
                if(dataArr.length > 1){alert('只能选择一个主设备');return false;}
                var idsStr = dataArr[0].id;
                var options = {
                    shadeClose: false,
                    shade: [0.3, '#393D49'],
                    callback: function (data) {
                       if(data.status == 1){
                           Toastr.success("成功");
                       }else{
                           Toastr.success("失败");
                       }
                    }
                }
                Fast.api.open('Equipmentinfo/index/equipment_id/'+ idsStr,'设备详情',options);
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            //
            $("#c-departments_id").data("params", function (obj) {
                return {custom: {hospital_id: $("#c-hospitals_id").val()}};
            });

            //所属卡种
            $('#c-hospitals_id').on('change', function () {
                var catid = $('#c-hospitals_id').val();
                $("#c-departments_id").selectPageClear();
            });





            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
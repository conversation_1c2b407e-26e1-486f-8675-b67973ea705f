/* packageB/pay/succeed.wxss */
.container{
	padding-top: 20rpx;
}
.pay{
	background: #fff;
	min-height: calc(100vh - 20rpx);
	box-sizing: border-box;
	padding-top: 140rpx;
	text-align: center;
}
.pay image{
	width: 272rpx;
	height: auto;
	margin: 0 auto 32rpx;
}
.pay .text{
	font-size: 32rpx;
	line-height: 46rpx;
	color: #333;
	margin-bottom: 150rpx;
}
.pay .btn{
	font-size: 30rpx;
	line-height: 86rpx;
	color: #fff;
	width: 540rpx;
	border-radius: 40rpx;
	background: linear-gradient(90deg, #2298F9 0%, #57B2FC 47%, #57B2FC 47%, #57B2FC 47%, #57B2FC 47%, #57B2FC 47%, #94CFFF 100%);
	box-shadow: 0px 8px 12px 1px #8CCBFF;
	margin: 0 auto;
}
<?php

namespace app\admin\model;

use think\Model;


class Equipment extends Model
{

    

    //数据库
    protected $connection = 'database';
    // 表名
    protected $name = 'equipment';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
//    protected $append = [
//        'status_text'
//    ];
    

    
    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }
    
    public function getHardwaretypeList()
    {
        return ['1' => __('mqtt地锁')];
    }


//    public function getStatusTextAttr($value, $data)
//    {
//        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
//        $list = $this->getStatusList();
//        return isset($list[$value]) ? $list[$value] : '';
//    }




    public function admin()
    {
        return $this->belongsTo('Admin', 'platform_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function agent()
    {
        return $this->belongsTo('Agent', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function hospital()
    {
        return $this->belongsTo('Hospital', 'hospitals_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function hospitaldepartments()
    {
        return $this->belongsTo('HospitalDepartments', 'departments_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}

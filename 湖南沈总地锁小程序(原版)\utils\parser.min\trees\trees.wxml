<wxs module="handler" src="./handler.wxs"/><block wx:for="{{nodes}}" wx:key="index"><rich-text wx:if="{{item.name=='img'}}" id="{{item.attrs.id}}" class="_img" style="text-indent:0;{{handler.getStyle(item.attrs.style,'inline-block')}}" nodes="{{handler.getNode(item,imgLoad)}}" data-attrs="{{item.attrs}}" bindtap="imgtap" bindlongpress="imglongtap"/><block wx:elif="{{item.type=='text'}}"><text wx:if="{{!item.decode}}" decode>{{item.text}}</text><rich-text wx:else style="display:inline-block" nodes="{{[item]}}"/></block><text wx:elif="{{item.name=='br'}}">\n</text><view wx:elif="{{item.name=='a'}}" class="_a {{item.attrs.class}}" hover-class="navigator-hover" style="{{item.attrs.style}}" data-attrs="{{item.attrs}}" bindtap="linkpress"><trees class="_node" nodes="{{item.children}}"/></view><block wx:elif="{{item.name=='video'}}"><view wx:if="{{item.lazyLoad && !controls[item.attrs.id].play}}" id="{{item.attrs.id}}" class="_video {{item.attrs.class}}" style="{{item.attrs.style}}" bindtap="loadVideo"/><video wx:else id="{{item.attrs.id}}" class="{{item.attrs.class}}" style="{{item.attrs.style}}" autoplay="{{item.attrs.autoplay||controls[item.attrs.id].play}}" controls="{{item.attrs.controls}}" loop="{{item.attrs.loop}}" muted="{{item.attrs.muted}}" poster="{{item.attrs.poster}}" src="{{controls[item.attrs.id]?item.attrs.source[controls[item.attrs.id].index]:item.attrs.src}}" unit-id="{{item.attrs['unit-id']}}" data-source="{{item.attrs.source}}" bindplay="play" binderror="videoError"/></block><audio wx:elif="{{item.name=='audio'}}" id="{{item.attrs.id}}" class="{{item.attrs.class}}" style="{{item.attrs.style}}" author="{{item.attrs.author}}" controls="{{item.attrs.controls}}" loop="{{item.attrs.loop}}" name="{{item.attrs.name}}" poster="{{item.attrs.poster}}" src="{{controls[item.attrs.id]?item.attrs.source[controls[item.attrs.id].index]:item.attrs.src}}" data-source="{{item.attrs.source}}" binderror="audioError"/><ad wx:elif="{{item.name=='ad'}}" class="{{item.attrs.class}}" style="{{item.attrs.style}}" unit-id="{{item.attrs['unit-id']}}" binderror="adError"/><view wx:elif="{{item.name=='li'}}" class="{{item.attrs.class}} _li" style="{{item.attrs.style}};display:flex"><view wx:if="{{item.type=='ol'}}" class="_ol-before">{{item.num}}</view><view wx:else class="_ul-before"><view wx:if="{{item.floor%3==0}}" class="_ul-type1">█</view><view wx:elif="{{item.floor%3==2}}" class="_ul-type2"/><view wx:else class="_ul-type1" style="border-radius:50%">█</view></view><trees class="_node" style="display:block" controls="{{controls}}" nodes="{{item.children}}"/></view><rich-text wx:elif="{{handler.useRichText(item)}}" id="{{item.attrs.id}}" class="__{{item.name}}" style="{{handler.getStyle(item.attrs.style,'block')}}" nodes="{{[item]}}"/><trees wx:else id="{{item.attrs.id}}" class="_node _{{item.name}} {{item.attrs.class}}" style="{{item.attrs.style}}" controls="{{controls}}" nodes="{{item.children}}"/></block>
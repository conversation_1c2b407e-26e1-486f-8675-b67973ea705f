<!--pages/index/my.wxml-->
<view class="container">
	<view class="top" style="padding-top:{{customBar}}px;">
		<view class="userinfor" wx:if="{{islogin}}" bindtap="getLogin">
			<view class="pic">
				<image src="/image/tx_tb.png"></image>
			</view>
			<view class="sqdl">点击授权登录</view>
		</view>

		<view class="userinfor" wx:else>
			<view class="pic">
				<image src="{{avatar}}"></image>
			</view>
			<view class="name">
				<view>{{nickname}}</view>
				<text wx:if="{{mobile}}">{{mobile}}</text>
				<button wx:else open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">授权获取手机号</button>
			</view>
		</view>

		<view class="package">
			<image src="/image/package_bj.png" mode="widthFix"></image>
			<view class="nr">
				<view class="xx">
					<view>套餐中心</view>
					<text>购买套餐，即可享受专属特权！</text>
				</view>
				<view class="btn" bindtap="goPackage">
					<view>购买套餐</view>
					<image src="/image/icon_jttb.png"></image>
				</view>
			</view>
		</view>
	</view>
	<view class="order">
		<view class="nr">
			<view class="title">我的订单</view>
			<view class="nav">
				<view class="xx" wx:for='{{order}}' bindtap="goOrder" data-type="{{item.type}}">
					<image src="{{item.pic}}"></image>
					<view>{{item.title}}</view>
				</view>
			</view>
		</view>
	</view>

	<view class="my">
		<view class="nav">
			<view class="nr" wx:for='{{nav}}' data-path='{{item.path}}' bindtap='goUrl'>
				<image src="{{item.pic}}"></image>
				<view>{{item.title}}</view>
			</view>
			<view class="kf" bindtap='call' data-tel='{{kftel}}'>
				<view class="xx">
					<image src="/image/icon_nav4.png"></image>
					<view>客服中心</view>
				</view>
				<text>{{kftel}}</text>
			</view>
		</view>
	</view>
</view>

const app = getApp()


/**
 * 生成发送指令数据
 * frame_type 帧类型码
 * data 数据域
*/
export function sendMsg(frame_type, data = '') {
	var value =  ['AA', '55', '', '01', '00','','OD'];
  value[2] = frame_type;
  value[5] = data;
  value[4] = crcVerification(arrtostr(value));
  // var valueString = arrtostr(value);
  var instructions = arrtostr(value);
  return instructions;
}


/**
 * 发送消息
*/
export function sendOut(that,type,fun){
	console.log('向蓝牙发送数据')
	if(type == 3){
		console.log('向蓝牙发送获取电量数据')
		var str = 'AA55 01 01 64 207B 0D';
	}else if(type == 1){
		//地锁降下
    var str = 'AA55 02 01 00 D190 0D';
  }else if(type == 2){
		//地锁升起
    var str = 'AA55 04 01 00 3191 0D';
  }
  console.log(str);
  var value = openBluetooth(str);
  wx.writeBLECharacteristicValue({
    deviceId: that.data.deviceId,
    serviceId: that.data.servicesUuid,
    characteristicId: that.data.characterWriteUuid,
    value: value,
    success: function (res) {
      console.log('数据发送成功：' + JSON.stringify(res));
      fun(true)
    }, fail: function (res) {
			console.log('数据发送失败：' + JSON.stringify(res));
      fun(false)
    }
  })
}

/**
 * 生成效验域
 * @param {*} buffer 
 */
export function validityDomain(hex) {
  var hexArr = hexstrTOhexarr(hex);
  var n = 0;
  for (var i = 0; i < hexArr.length; i++) {
    n = n + hexdec(hexArr[i]);
  }
  var i = 504;
  var a = n & 255;  // == 89
  var b = a ^ 255;
  return dechex(b);
}

/**
 * 16进制转10进制
 * @param {*} hex 
 */
export function hexdec(hex) {
  return parseInt(hex, 16);
}

/**
 * 10进制转16进制
 * @param {*} hex 
 */
export function dechex(str) {
  var hex = str.toString(16);
  var na = hex.length % 2;
  if (hex.length % 2 != 0) {
    hex = 0 + hex;
  }
  return hex;
}

/**
 * buffer数据转16进制数据
 * @param {*} buffer 
 */
export function buffer2hex(buffer) {
  var hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)
    }
  )
  return hexArr.join(',');
}

/**
 * 16进制数据转buffer数据
 * @param {*} hex 
 */
export function openBluetooth(hex) {
  var typedArray = new Uint8Array(hex.match(/[\da-f]{2}/gi).map(function (h) {
    return parseInt(h, 16)
  }))
  return typedArray.buffer;
}

/**
 * 16进制字符串转数组
 * @param {*} hex 
 */
export function hexstrTOhexarr(hex) {
  var hexArr = [];
  var n = 0;
  for (var i = 0; i < hex.length; i += 2) {
    hexArr.push(hex.slice(n, n + 2));
    n = n + 2;
  }
  return hexArr;
}


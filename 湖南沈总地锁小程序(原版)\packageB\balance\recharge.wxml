<!--packageB/balance/recharge.wxml-->
<view class="container">
  <!-- 充值金额输入 -->
  <view class="amount-section">
    <view class="amount-title">充值金额</view>
    <view class="amount-input-wrapper">
      <text class="currency-symbol">¥</text>
      <input 
        class="amount-input" 
        type="digit" 
        placeholder="请输入金额充值" 
        value="{{amount}}"
        bindinput="onAmountInput"
        placeholder-style="color: #ccc"
      />
    </view>
  </view>

  <!-- 快捷金额选择 -->
  <view class="quick-amount-section">
    <view class="quick-amount-grid">
      <view 
        class="quick-amount-item {{selectedQuickAmount == item ? 'selected' : ''}}" 
        wx:for="{{quickAmounts}}" 
        wx:key="index"
        data-amount="{{item}}"
        bindtap="selectQuickAmount"
      >
        ¥{{item}}
      </view>
    </view>
  </view>

  <!-- 充值说明 -->
  <view class="recharge-tips">
    <view class="tips-title">充值说明</view>
    <view class="tips-content">
      <view class="tip-item">1. 充值可自定义金额</view>
      <view class="tip-item">2. 充值金额只能在本程序中使用充值金额无法提现，无法找零</view>
      <view class="tip-item">3. 充值金额永久有效</view>
    </view>
  </view>

  <!-- 确认充值按钮 -->
  <view class="recharge-btn-wrapper">
    <button 
      class="recharge-btn {{loading ? 'loading' : ''}}" 
      bindtap="confirmRecharge"
      disabled="{{loading}}"
    >
      {{loading ? '处理中...' : '确认充值'}}
    </button>
  </view>
</view>

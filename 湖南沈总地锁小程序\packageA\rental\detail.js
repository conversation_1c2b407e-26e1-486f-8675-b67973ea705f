// packageA/rental/detail.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		statusBar: app.globalData.statusBar,
		customBar: app.globalData.customBar,
		custom: app.globalData.custom,

		ordercode: '',// 订单编号

		order: '',// 订单信息
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.setData({
			ordercode: options.ordercode
		})
		this.getOrderInfo()
	},

	goBack(){
		wx.navigateBack();
	},


	// 获取订单详情
	getOrderInfo() {
		let that = this
		let params = {
			ordercode: that.data.ordercode
		}
		app.post('Order/get_order_info', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					order: data
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {
			wx.showToast({
				title: '网络错误，请重试',
				icon: 'none',
				duration: 2000
			})
		})
	},

	// 跳转
	goUrl(e) {
		wx.navigateTo({
			url: e.currentTarget.dataset.path,
		})
	},

	// 购买套餐
	goPackage() {
		wx.navigateTo({
			url: '/packageA/package/index',
		})
	},

	// 短时免单-套餐抵扣
	getPay(){
		let that = this
		let pay_status = that.data.order.pay_status
		let text = ''
		let params = {
			ordercode: that.data.ordercode
		}
		app.post('Order/orderFreeSheet', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				if(pay_status == 3){
					text = '车锁费用已免单~'
				}else{
					text = '车锁费用已用套餐抵扣~'
				}
				wx.showModal({
					title: '提示',
					content: text,
					showCancel: false,
					confirmText: '我知道了',
					success (res) {
						if (res.confirm) {
							wx.switchTab({
								url: '/pages/index/index',
							})
						}
					}
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 去支付
	goPay(){
		wx.navigateTo({
			url: '/packageB/pay/index?money=' + this.data.order.money + '&ordercode=' + this.data.ordercode,
		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
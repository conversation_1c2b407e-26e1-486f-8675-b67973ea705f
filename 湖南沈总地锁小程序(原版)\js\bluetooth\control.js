const agreement = require('agreement.js');
const link = require('link.js');
const app = getApp()

//1、下发唤醒主机指令
export function huanxing(that, i) {
  console.log('首页', that.data);
  console.log('第' + i + '次下发唤醒主机指令');
  var hex = 'BD11A1000102030405060708090A0B0C0D0E0F';
  var hex = hex + agreement.validityDomain(hex);
  agreement.sendOut(that, hex, function (e) {
    //数据发送成功
    if (i < 2) {
      i = i + 1;
      setTimeout(function () {
        huanxing(that, i);
      }, 500);
    } else {
      // console.log('唤醒指令两次发送完成');
      // setTimeout(function () {
      //   shenqingkongzhi(that);
      // }, 1000);
    }
  }, function (e) {
    //数据发送失败
    wx.hideLoading();
    wx.showToast({
      title: '申请控制失败',
      icon: 'none',
      duration: 2000
    })
    that.setData({
      bluetooth_type: 0,
      operation_type: 0,
      sign_type: '',
      pke_type: '00',
      isSignal: false,
    });
    // let params = {
    //   key_id: that.data.carid,
    //   behavior_type: 5,
    //   bluetooth_data: wx.getStorageSync("bluetooth_data"),
    //   status: 2,
    //   answer_data: '下发唤醒主机指令失败',
    // }
    // link.getueserMember(params)
  });
}

//2、申请控制
export function shenqingkongzhi(that) {
  console.log('app申请控制指令');
  wx.showLoading({
    title: '控制申请中...'
  })
  var hex = 'BD11A2000102030405060708090A0B0C0D0E0F';
  var hex = hex + agreement.validityDomain(hex);

  agreement.sendOut(that, hex, function (e) {
    that.data.timer = setInterval(function () {
      if (that.data.operation_type != 0) {
        wx.hideLoading();
        clearInterval(that.data.timer)
        wx.showToast({
          title: '控制申请无应答',
          icon: 'none',
          duration: 2000
        })
        if (that.data.operation_type == 1) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 5,
            operation_type: 2,
            operation_position: 1,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '申请关锁指令无应答',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
          });
        } else if (that.data.operation_type == 2) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 5,
            operation_type: 1,
            operation_position: 1,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '申请开锁指令无应答',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
          });
        } else if (that.data.operation_type == 3) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 5,
            operation_type: 1,
            operation_position: 3,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '申请开后备箱指令无应答',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
          });
        } else if (that.data.operation_type == 4) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 5,
            operation_type: 1,
            operation_position: 4,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '申请寻车指令无应答',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
          });
        } else if (that.data.operation_type == 5 && that.data.sign_type == 0) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 8,
            operation_type: 2,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '申请关闭信号调节指令无应答',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
            sign_type: '',
            isSignal: false,
          });
        } else if (that.data.operation_type == 5 && that.data.sign_type == 1) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 8,
            operation_type: 1,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '申请开启信号强度设置指令无应答',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
            sign_type: '',
            pke_type: '00',
            isSignal: false,
          });
        }
      } else {

      }
    }, 20000)
  }, function (e) {
    if (that.data.operation_type == 1) {
      let params = {
        key_id: that.data.carid,
        behavior_type: 5,
        operation_type: 2,
        operation_position: 1,
        bluetooth_data: wx.getStorageSync("bluetooth_data"),
        status: 2,
        answer_data: '申请关锁指令发送失败',
      }
      link.getueserMember(params)
      that.setData({
        operation_type: 0,
      });
    } else if (that.data.operation_type == 2) {
      let params = {
        key_id: that.data.carid,
        behavior_type: 5,
        operation_type: 1,
        operation_position: 1,
        bluetooth_data: wx.getStorageSync("bluetooth_data"),
        status: 2,
        answer_data: '申请开锁指令发送失败',
      }
      link.getueserMember(params)
      that.setData({
        operation_type: 0,
      });
    } else if (that.data.operation_type == 3) {
      let params = {
        key_id: that.data.carid,
        behavior_type: 5,
        operation_type: 1,
        operation_position: 3,
        bluetooth_data: wx.getStorageSync("bluetooth_data"),
        status: 2,
        answer_data: '申请开后备箱指令发送失败',
      }
      link.getueserMember(params)
      that.setData({
        operation_type: 0,
      });
    } else if (that.data.operation_type == 4) {
      let params = {
        key_id: that.data.carid,
        behavior_type: 5,
        operation_type: 1,
        operation_position: 4,
        bluetooth_data: wx.getStorageSync("bluetooth_data"),
        status: 2,
        answer_data: '申请寻车指令发送失败',
      }
      link.getueserMember(params)
      that.setData({
        operation_type: 0,
      });
    } else if (that.data.operation_type == 5 && that.data.sign_type == 0) {
      let params = {
        key_id: that.data.carid,
        behavior_type: 8,
        operation_type: 2,
        bluetooth_data: wx.getStorageSync("bluetooth_data"),
        status: 2,
        answer_data: '申请关闭信号调节指令无应答',
      }
      link.getueserMember(params)
      that.setData({
        operation_type: 0,
        sign_type: '',
        isSignal: false,
      });
    } else if (that.data.operation_type == 5 && that.data.sign_type == 1) {
      let params = {
        key_id: that.data.carid,
        behavior_type: 8,
        operation_type: 1,
        bluetooth_data: wx.getStorageSync("bluetooth_data"),
        status: 2,
        answer_data: '开启信号强度设置指令无应答',
      }
      link.getueserMember(params)
      that.setData({
        operation_type: 0,
        sign_type: '',
        pke_type: '00',
        isSignal: false,
      });
    }
  });
}

//3、发送控制指令
export function kongzhi(that, str) {
  console.log(that.data.operation_type)
  wx.showLoading({
    title: '控制指令发送中...'
  })
  if (that.data.operation_type == 1) {
    console.log('app发送控制指令-关锁控制');
    var hex = 'BD11A4';
    var data = '4D482D42542D436D642D4C6F636B2121' + str;
    var request_data = { 'step': 2, 'str': data, 'action': 'iOS_AES_Key2_Encryption', };
    app.post('index/aesjjm', request_data).then(res => {
      const {
        code,
        data,
        msg
      } = res
      //接口数据
      if (code == 1) {
        console.log(data)
        hex = hex + data;
        hex = hex + agreement.validityDomain(hex);
        agreement.sendOut(that, hex, function (e) {
          that.data.timer = setInterval(function () {
            if (that.data.operation_type != 0) {
              wx.hideLoading();
              clearInterval(that.data.timer)
              wx.showToast({
                title: '控制指令发送无应答',
                icon: 'none',
                duration: 2000
              })
              let params = {
                key_id: that.data.carid,
                behavior_type: 5,
                operation_type: 2,
                operation_position: 1,
                bluetooth_data: wx.getStorageSync("bluetooth_data"),
                status: 2,
                answer_data: '关锁控制指令无应答',
              }
              link.getueserMember(params)
              that.setData({
                operation_type: 0,
              });
            } else {

            }
          }, 20000)
        }, function (e) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 5,
            operation_type: 2,
            operation_position: 1,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '发送关锁控制指令失败',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
          });
        });
      } else {
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {

    })
  } else if (that.data.operation_type == 2) {
    console.log('app发送控制指令-开锁控制');
    var hex = 'BD11A5';
    var data = '4D482D42542D436D642D556E4C6F636B' + str;
    var request_data = { 'step': 2, 'str': data, 'action': 'iOS_AES_Key2_Encryption', };
    app.post('index/aesjjm', request_data).then(res => {
      const {
        code,
        data,
        msg
      } = res
      //接口数据
      if (code == 1) {
        console.log(data)
        hex = hex + data;
        hex = hex + agreement.validityDomain(hex);
        agreement.sendOut(that, hex, function (e) {
          that.data.timer = setInterval(function () {
            console.log(times)
            if (that.data.operation_type != 0) {
              wx.hideLoading();
              clearInterval(that.data.timer)
              wx.showToast({
                title: '设备无应答',
                icon: 'none',
                duration: 2000
              })
              let params = {
                key_id: that.data.carid,
                behavior_type: 5,
                operation_type: 1,
                operation_position: 1,
                bluetooth_data: wx.getStorageSync("bluetooth_data"),
                status: 2,
                answer_data: '开锁控制指令无应答',
              }
              link.getueserMember(params)
              that.setData({
                operation_type: 0,
              });
            } else {

            }
          }, 20000)
        }, function (e) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 5,
            operation_type: 1,
            operation_position: 1,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '发送开锁控制指令失败',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
          });
        });
      } else {
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {

    })
  } else if (that.data.operation_type == 3) {
    console.log('app发送控制指令-开尾箱控制');
    var hex = 'BD11C1';
    var data = '4D482D42542D436D642D556E626F6F74' + str;
    var request_data = { 'step': 2, 'str': data, 'action': 'iOS_AES_Key2_Encryption', };
    app.post('index/aesjjm', request_data).then(res => {
      const {
        code,
        data,
        msg
      } = res
      //接口数据
      if (code == 1) {
        console.log(data)
        hex = hex + data;
        hex = hex + agreement.validityDomain(hex);
        agreement.sendOut(that, hex, function (e) {
          that.data.timer = setInterval(function () {
            if (that.data.operation_type != 0) {
              wx.hideLoading();
              clearInterval(that.data.timer)
              wx.showToast({
                title: '设备无应答',
                icon: 'none',
                duration: 2000
              })
              let params = {
                key_id: that.data.carid,
                behavior_type: 5,
                operation_type: 1,
                operation_position: 3,
                bluetooth_data: wx.getStorageSync("bluetooth_data"),
                status: 2,
                answer_data: '开尾箱控制指令无应答',
              }
              link.getueserMember(params),
                that.setData({
                  operation_type: 0,
                });
            } else {

            }
          }, 20000)
        }, function (e) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 5,
            operation_type: 1,
            operation_position: 3,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '发送开尾箱控制指令失败',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
          });
        });
      } else {
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {

    })
  } else if (that.data.operation_type == 4) {
    console.log('app发送控制指令-寻车控制');
    var hex = 'BD11C2';
    var data = '4D482D42542D436D642D416C61726D21' + str;
    var request_data = { 'step': 2, 'str': data, 'action': 'iOS_AES_Key2_Encryption', };
    app.post('index/aesjjm', request_data).then(res => {
      const {
        code,
        data,
        msg
      } = res
      //接口数据
      if (code == 1) {
        console.log(data)
        hex = hex + data;
        hex = hex + agreement.validityDomain(hex);
        agreement.sendOut(that, hex, function (e) {
          that.data.timer = setInterval(function () {
            if (that.data.operation_type != 0) {
              wx.hideLoading();
              clearInterval(that.data.timer)
              wx.showToast({
                title: '设备无应答',
                icon: 'none',
                duration: 2000
              })
              let params = {
                key_id: that.data.carid,
                behavior_type: 5,
                operation_type: 1,
                operation_position: 4,
                bluetooth_data: wx.getStorageSync("bluetooth_data"),
                status: 2,
                answer_data: '寻车控制指令无应答',
              }
              link.getueserMember(params)
              that.setData({
                operation_type: 0,
              });
            } else {

            }
          }, 20000)
        }, function (e) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 5,
            operation_type: 1,
            operation_position: 4,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '发送寻车控制指令失败',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
          });
        });
      } else {
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {

    })
  } else if (that.data.operation_type == 5 && that.data.sign_type == 0) {
    console.log('app发送控制指令-关闭信号调节');
    var hex = 'BD11C6';
    var data = '08000102030405060708090A0B0C0D0E' + str;
    var request_data = { 'step': 2, 'str': data, 'action': 'iOS_AES_Key2_Encryption', };
    app.post('index/aesjjm', request_data).then(res => {
      const {
        code,
        data,
        msg
      } = res
      //接口数据
      if (code == 1) {
        console.log(data)
        hex = hex + data;
        hex = hex + agreement.validityDomain(hex);
        agreement.sendOut(that, hex, function (e) {
          that.data.timer = setInterval(function () {
            if (that.data.operation_type != 0) {
              wx.hideLoading();
              clearInterval(that.data.timer)
              wx.hideLoading();
              wx.showToast({
                title: '设备无应答',
                icon: 'none',
                duration: 2000
              })
              let params = {
                key_id: that.data.carid,
                behavior_type: 8,
                operation_type: 2,
                bluetooth_data: wx.getStorageSync("bluetooth_data"),
                status: 2,
                answer_data: '关闭信号调节指令无应答',
              }
              link.getueserMember(params)
              that.setData({
                operation_type: 0,
                sign_type: '',
                isSignal: false,
              });
            } else {

            }
          }, 20000)
        }, function (e) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 8,
            operation_type: 2,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '关闭信号调节指令失败',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
            sign_type: '',
            isSignal: false,
          });
        });
      } else {
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {

    })
  } else if (that.data.operation_type == 5 && that.data.sign_type == 1) {
    console.log('app发送控制指令-开启信号强度设置');
    var hex = 'BD11C6';
    var data = '09' + that.data.pke_type + '0102030405060708090A0B0C0D0E' + str;;
    var request_data = { 'step': 2, 'str': data, 'action': 'iOS_AES_Key2_Encryption', };
    app.post('index/aesjjm', request_data).then(res => {
      const {
        code,
        data,
        msg
      } = res
      //接口数据
      if (code == 1) {
        console.log(data)
        hex = hex + data;
        hex = hex + agreement.validityDomain(hex);
        agreement.sendOut(that, hex, function (e) {
          that.data.timer = setInterval(function () {
            if (that.data.operation_type != 0) {
              wx.hideLoading();
              clearInterval(that.data.timer)
              wx.showToast({
                title: '设备无应答',
                icon: 'none',
                duration: 2000
              })
              let params = {
                key_id: that.data.carid,
                behavior_type: 8,
                operation_type: 1,
                bluetooth_data: wx.getStorageSync("bluetooth_data"),
                status: 2,
                answer_data: '开启信号强度设置指令无应答',
              }
              link.getueserMember(params)
              that.setData({
                operation_type: 0,
                sign_type: '',
                pke_type: '00',
                isSignal: false,
              });
            } else {

            }
          }, 20000)
        }, function (e) {
          let params = {
            key_id: that.data.carid,
            behavior_type: 8,
            operation_type: 1,
            bluetooth_data: wx.getStorageSync("bluetooth_data"),
            status: 2,
            answer_data: '开启信号强度设置指令失败',
          }
          link.getueserMember(params)
          that.setData({
            operation_type: 0,
            sign_type: '',
            pke_type: '00',
            isSignal: false,
          });
        });
      } else {
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {

    })
  }
}


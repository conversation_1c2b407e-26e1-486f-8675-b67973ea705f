// 小程序富文本插件 https://github.com/jin-yufeng/Parser
"use strict"; function hash(t) { for (var e = t.length, i = 5381; e--;)i += (i << 5) + t.charCodeAt(e); return i } var _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (t) { return typeof t } : function (t) { return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t }, cache = getApp().parserCache = {}, config = require("./libs/config.js"), CssHandler = require("./libs/CssHandler.js"), document; try { document = require("./libs/document.js") } catch (t) { } var fs = wx.getFileSystemManager ? wx.getFileSystemManager() : null, parseHtml = require("./libs/MpHtmlParser.js"), showAnimation = wx.createAnimation({ timingFunction: "ease" }).opacity(1).step().export(); Component({ properties: { html: { type: null, observer: function (t) { if (this._refresh) return this._refresh = !1; this.setContent(t, !0) } }, autosetTitle: { type: Boolean, value: !0 }, autopause: { type: Boolean, value: !0 }, domain: String, gestureZoom: Boolean, lazyLoad: Boolean, selectable: Boolean, tagStyle: Object, showWithAnimation: Boolean, useAnchor: Boolean, useCache: Boolean }, created: function () { var t = this; this.navigateTo = function (e) { if (e.fail = e.fail || function () { }, !t.data.useAnchor) return e.fail({ errMsg: "Use-anchor attribute is disabled" }); t.createSelectorQuery().select("#root" + (e.id ? ">>>#" + e.id : "")).boundingClientRect().selectViewport().scrollOffset().exec(function (t) { if (!t[0]) return e.fail({ errMsg: "Label not found" }); wx.pageScrollTo({ scrollTop: t[1].scrollTop + t[0].top, success: e.success, fail: e.fail }) }) }, this.getText = function () { function e(t) { if (t) for (var a, n = 0; a = t[n++];)if ("text" == a.type) i += a.text; else if ("br" == a.type) i += "\n"; else { var s = "p" == a.name || "div" == a.name || "tr" == a.name || "li" == a.name || "h" == a.name[0] && a.name[1] > "0" && a.name[1] < "7"; s && i && "\n" != i[i.length - 1] && (i += "\n"), e(a.children), s && "\n" != i[i.length - 1] ? i += "\n" : "td" != a.name && "th" != a.name || (i += "\t") } } var i = ""; return e(t.data.html), i.replace(/&nbsp;/g, " ") }, this.getVideoContext = function (e) { if (!e) return t.videoContexts; for (var i = t.videoContexts.length; i--;)if (t.videoContexts[i].id == e) return t.videoContexts[i]; return null }, this.imgList = [], this.imgList.setItem = function (t, e) { var i = this; if (this[t] = e, e.includes("base64")) { var a = e.match(/data:image\/(\S+?);base64,(\S+)/); if (!a) return; var n = wx.env.USER_DATA_PATH + "/" + Date.now() + "." + a[1]; fs && fs.writeFile({ filePath: n, data: a[2], encoding: "base64", success: function () { return i[t] = n } }) } else if (this.includes(e)) { if ("http" != e.substring(0, 4)) return; for (var s = "", o = 0; o < e.length && (s += Math.random() >= .5 ? e[o].toUpperCase() : e[o].toLowerCase(), "/" != e[o] || "/" == e[o - 1] || "/" == e[o + 1]); o++); s += e.substring(o + 1), this[t] = s } }, this.imgList.each = function (t) { for (var e = 0; e < this.length; e++) { var i = t(this[e], e, this); i && this.setItem(e, i) } }, this._refresh = !1, this.setContent = function (e, i) { var a = { controls: {} }; if (t.data.showWithAnimation && (a.showAnimation = showAnimation), e) if ("string" == typeof e) { if (t.data.useCache) { var n = hash(e); cache[n] ? a.html = cache[n] : (a.html = parseHtml(e, t.data), cache[n] = a.html) } else a.html = parseHtml(e, t.data); t.triggerEvent("parse", a.html) } else if (e.constructor == Array) { if (e.length && "Parser" != e[0].PoweredBy) { var s = { _imgNum: 0, _videoNum: 0, _audioNum: 0, _domain: t.data.domain, _protocol: t.data.domain && t.data.domain.includes("://") ? t.data.domain.split("://")[0] : "http", _STACK: [], CssHandler: new CssHandler(t.data.tagStyle) }; !function t(e) { for (var i, a = 0; i = e[a++];)if ("text" != i.type) { i.attrs = i.attrs || {}; for (var n in i.attrs) config.trustAttrs[n] ? "string" != typeof i.attrs[n] && (i.attrs[n] = i.attrs[n].toString()) : i.attrs[n] = void 0; config.LabelHandler(i, s), config.blockTags[i.name] ? i.name = "div" : config.trustTags[i.name] || (i.name = "span"), i.children && i.children.length ? (s._STACK.push(i), t(i.children), s._STACK.pop()) : i.children = void 0 } }(e), a.html = e } i || (a.html = e) } else { if ("object" != (void 0 === e ? "undefined" : _typeof(e)) || !e.nodes) return console.warn("错误的 html 类型：" + (void 0 === e ? "undefined" : _typeof(e))); a.html = e.nodes, console.warn("错误的 html 类型：object 类型已废弃，请直接将 html 设置为 object.nodes") } else { if (i) return; a.html = "" } t._refresh = !!a.html, t.setData(a), t.imgList.length = 0, t.videoContexts = [], document && (t.document = new document("html", a.html || e, t)); for (var o = t.selectAllComponents("#root,#root>>>._node"), r = o.length; r--;) { var l, h, c, u; !function () { var e = o[r]; for (e._top = t, l = !!e._observer, h = e.data.nodes.length; c = e.data.nodes[--h];)c.c || ("img" == c.name ? (c.attrs.src && c.attrs.i && t.imgList.setItem(c.attrs.i, c.attrs.src), l || (l = !0, t.data.lazyLoad && e.createIntersectionObserver ? (wx.nextTick || setTimeout)(function () { e._observer = e.createIntersectionObserver(), e._observer.relativeToViewport({ top: 1e3, bottom: 1e3 }).observe("._img", function () { e.setData({ imgLoad: !0 }), e._observer.disconnect(), e._observer = void 0 }) }, 50) : e.setData({ imgLoad: !0 }))) : "video" == c.name ? (u = wx.createVideoContext(c.attrs.id, e), u.id = c.attrs.id, t.videoContexts.push(u)) : "audio" == c.name && c.attrs.autoplay ? wx.createAudioContext(c.attrs.id, e).play() : "title" == c.name && t.data.autosetTitle && "text" == c.children[0].type && c.children[0].text && wx.setNavigationBarTitle({ title: c.children[0].text })) }() } (wx.nextTick || setTimeout)(function () { t.createSelectorQuery().select("#root").boundingClientRect(function (e) { t.width = e.width, t.triggerEvent("ready", e) }).exec() }, 50) } }, detached: function () { this.imgList.each(function (t) { t && t.includes(wx.env.USER_DATA_PATH) && fs && fs.unlink({ filePath: t }) }) }, methods: { tap: function (t) { if (this.data.gestureZoom && t.timeStamp - this.lastTime < 300) { if (this.zoomIn) this.animation.translateX(0).scale(1).step(), wx.pageScrollTo({ scrollTop: (t.detail.y - t.currentTarget.offsetTop + this.initY) / 2 - t.touches[0].clientY, duration: 400 }); else { var e = t.detail.x - t.currentTarget.offsetLeft; this.initY = t.detail.y - t.currentTarget.offsetTop, this.animation = wx.createAnimation({ transformOrigin: e + "px " + this.initY + "px 0", timingFunction: "ease-in-out" }), this.animation.scale(2).step(), this.translateMax = e / 2, this.translateMin = (e - this.width) / 2, this.translateX = 0 } this.zoomIn = !this.zoomIn, this.setData({ showAnimation: this.animation.export() }) } this.lastTime = t.timeStamp }, touchstart: function (t) { 1 == t.touches.length && (this.initX = this.lastX = t.touches[0].pageX) }, touchmove: function (t) { var e = t.touches[0].pageX - this.lastX; if (this.zoomIn && 1 == t.touches.length && Math.abs(e) > 20) { if (this.lastX = t.touches[0].pageX, this.translateX <= this.translateMin && e < 0 || this.translateX >= this.translateMax && e > 0) return; this.translateX += e * Math.abs(this.lastX - this.initX) * .05, this.translateX < this.translateMin && (this.translateX = this.translateMin), this.translateX > this.translateMax && (this.translateX = this.translateMax), this.animation.translateX(this.translateX).step(), this.setData({ showAnimation: this.animation.export() }) } } } });
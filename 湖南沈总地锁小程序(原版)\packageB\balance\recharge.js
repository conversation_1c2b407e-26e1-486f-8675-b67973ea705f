// packageB/balance/recharge.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    amount: '', // 充值金额
    quickAmounts: [10, 20, 50, 100, 200, 500], // 快捷金额
    selectedQuickAmount: 0, // 选中的快捷金额
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 输入充值金额
   */
  onAmountInput(e) {
    let value = e.detail.value
    // 限制小数点后两位
    if (value.includes('.')) {
      let parts = value.split('.')
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2)
      }
    }
    
    this.setData({
      amount: value,
      selectedQuickAmount: 0 // 清除快捷金额选择
    })
  },

  /**
   * 选择快捷金额
   */
  selectQuickAmount(e) {
    const amount = e.currentTarget.dataset.amount
    this.setData({
      amount: amount.toString(),
      selectedQuickAmount: amount
    })
  },

  /**
   * 验证充值金额
   */
  validateAmount() {
    const amount = parseFloat(this.data.amount)
    
    if (!this.data.amount || isNaN(amount)) {
      wx.showToast({
        title: '请输入充值金额',
        icon: 'none',
        duration: 2000
      })
      return false
    }
    
    if (amount < 0.01) {
      wx.showToast({
        title: '充值金额不能少于0.01元',
        icon: 'none',
        duration: 2000
      })
      return false
    }
    
    if (amount > 10000) {
      wx.showToast({
        title: '单次充值金额不能超过10000元',
        icon: 'none',
        duration: 2000
      })
      return false
    }
    
    return true
  },

  /**
   * 确认充值
   */
  confirmRecharge() {
    if (!this.validateAmount()) {
      return
    }
    
    if (this.data.loading) {
      return
    }
    
    this.setData({ loading: true })
    
    // 第一步：创建充值订单
    this.createRechargeOrder()
  },

  /**
   * 创建充值订单
   */
  createRechargeOrder() {
    let that = this
    let params = {
      amount: parseFloat(that.data.amount)
    }
    
    app.post('Order/rechargeCreate', params).then(res => {
      const { code, data, msg } = res
      if (code == 1) {
        // 创建成功，发起支付
        that.startPayment(data.pay_id)
      } else {
        that.setData({ loading: false })
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {
      that.setData({ loading: false })
      wx.showToast({
        title: '创建充值订单失败',
        icon: 'none',
        duration: 2000
      })
      console.log('创建充值订单失败', err)
    })
  },

  /**
   * 发起支付
   */
  startPayment(payId) {
    let that = this
    let params = {
      pay_id: payId
    }
    
    app.post('Order/rechargePay', params).then(res => {
      const { code, data, msg } = res
      if (code == 1) {
        // 调用微信支付
        that.callWechatPay(data)
      } else {
        that.setData({ loading: false })
        wx.showToast({
          title: msg,
          icon: 'none',
          duration: 2000
        })
      }
    }).catch((err) => {
      that.setData({ loading: false })
      wx.showToast({
        title: '发起支付失败',
        icon: 'none',
        duration: 2000
      })
      console.log('发起支付失败', err)
    })
  },

  /**
   * 调用微信支付
   */
  callWechatPay(payData) {
    let that = this
    
    wx.requestPayment({
      timeStamp: payData.timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType,
      paySign: payData.paySign,
      success() {
        that.setData({ loading: false })
        wx.showToast({
          title: "充值成功",
          icon: "success",
          duration: 2000
        })
        
        setTimeout(() => {
          wx.navigateBack()
        }, 2000)
      },
      fail(res) {
        that.setData({ loading: false })
        console.log('支付失败', res)
        
        if (res.errMsg.includes('cancel')) {
          wx.showToast({
            title: "支付已取消",
            icon: "none",
            duration: 2000
          })
        } else {
          wx.showToast({
            title: "支付失败",
            icon: "none",
            duration: 2000
          })
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})

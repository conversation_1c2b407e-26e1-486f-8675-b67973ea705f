/* pages/index/my.wxss */
.top{
	padding: 0 30rpx;
	background: linear-gradient(5deg, #F8FAFA 0%, rgba(208,235,246,0.7600) 30%, #B3D6FF 100%);
	margin-bottom: 24rpx;
}
.top .userinfor{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	padding: 0 12rpx;
	margin-bottom: 60rpx;
}
.top .userinfor .pic{
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	overflow: hidden;
}
.top .userinfor .pic image{
	width: 100%;
	height: 100%;
}
.top .userinfor .sqdl{
	font-size: 36rpx;
	line-height: 50rpx;
	color: #333;
	margin-left: 37rpx;
	font-weight: bold;
}
.top .userinfor .name{
	margin-left: 37rpx;
}
.top .userinfor .name view{
	font-size: 36rpx;
	line-height: 50rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 6rpx;
}
.top .userinfor .name text{
	font-size: 24rpx;
	line-height: 34rpx;
	color: #999;
	display: block;
}
.top .userinfor .name button{
	font-size: 24rpx;
	line-height: 34rpx;
	color: #999;
	display: block;
	background: none;
	padding: 0;
}
.top .userinfor .name button::after{
	border: none;
}
.top .package{
	position: relative;
}
.top .package > image{
	width: 100%;
	height: auto;
	display: block;
}
.top .package .nr{
	position: absolute;
	left: 0;
	top: 0;
	padding: 30rpx 60rpx 0 50rpx;
	width: 100%;
	box-sizing: border-box;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: space-between;
}
.top .package .nr .xx view{
	font-size: 32rpx;
	line-height: 42rpx;
	color: #fff;
	font-weight: bold;
	margin-bottom: 10rpx;
}
.top .package .nr .xx text{
	font-size: 26rpx;
	line-height: 36rpx;
	color: rgba(255, 255, 255, 0.6);
}
.top .package .nr .btn{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	background: #fff;
	padding: 0 24rpx;
	border-radius: 34rpx;
}
.top .package .nr .btn view{
	font-size: 26rpx;
	line-height: 64rpx;
	color: #299DFC;
}
.top .package .nr .btn image{
	width: 28rpx;
	height: 28rpx;
	margin-left: 10rpx;
}
.order{
	padding: 0 30rpx;
	margin-bottom: 20rpx;
}
.order .nr{
	background: #fff;
	border-radius: 20rpx;
	padding: 27rpx 30rpx 34rpx;
}
.order .nr .title{
	font-size: 30rpx;
	line-height: 42rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 26rpx;
}
.order .nr .nav{
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}
.order .nr .nav .xx{
	text-align: center;
}
.order .nr .nav .xx image{
	width: 48rpx;
	height: 48rpx;
	margin-bottom: 18rpx;
}
.order .nr .nav .xx view{
	font-size: 26rpx;
	line-height: 36rpx;
	color: #333;
}
.my{
	padding: 0 30rpx;
}
.my .nav{
	background: #fff;
	border-radius: 20rpx;
	padding: 0 30rpx;
}
.my .nav .nr{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAXCAYAAAA/ZK6/AAAAAXNSR0IArs4c6QAAAZZJREFUOE+V089KW0EUBvDvu2aom6ZC21UWLgtdGtyVNkaLJmEyO5/A53DlO7hw6wOESZNRm66FUupGxH+FQltRggsVsrj3JkcmJJKamxhne77fnMP8odZ6PY7jTedcExMsGmMOALwQkQVr7dVThuVyuUnyjYicAMhbay/GIQ8+kvwC4KWInEVRlHfO/R2F6AulUimbSqV2AbwG8IvkUqVS+Z2EusCvQqHwXim1RzID4I+I+PHOH6MH4AvFYnHWdyL5TkT+xXGcr9frp4PoP9Dr9FYptUNyDsBFp9NZrFarx300BHoorZSqkfwgIpdRFC065458LRH4Qi6Xm06n0xWSyyLSbLfb+VqtdjgSeJTNZlUmk9kmuQrg2h/EWOCR1no+CIJ9AFMisjsWaK3ngiD4BmBGRH60Wq2lkSAp3Gg0bhJBb4y9wZ19OPGUeuGvAF71x+iHh8BgGMD3MAw/O+duE296kvBDh0nDXfCccBcYY7YArAHYD8Nw5fHMQ8/bGLPhv6aILFtr757801rrTyR/ThL2m90DkinTrTO74aMAAAAASUVORK5CYII=) no-repeat right center;
	background-size: 12rpx auto;
}
.my .nav .nr image{
	width: 45rpx;
	height: 45rpx;
}
.my .nav .nr view{
	font-size: 28rpx;
	line-height: 100rpx;
	color: #333;
	margin-left: 25rpx;
}
.my .nav .kf{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: space-between;
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAXCAYAAAA/ZK6/AAAAAXNSR0IArs4c6QAAAZZJREFUOE+V089KW0EUBvDvu2aom6ZC21UWLgtdGtyVNkaLJmEyO5/A53DlO7hw6wOESZNRm66FUupGxH+FQltRggsVsrj3JkcmJJKamxhne77fnMP8odZ6PY7jTedcExMsGmMOALwQkQVr7dVThuVyuUnyjYicAMhbay/GIQ8+kvwC4KWInEVRlHfO/R2F6AulUimbSqV2AbwG8IvkUqVS+Z2EusCvQqHwXim1RzID4I+I+PHOH6MH4AvFYnHWdyL5TkT+xXGcr9frp4PoP9Dr9FYptUNyDsBFp9NZrFarx300BHoorZSqkfwgIpdRFC065458LRH4Qi6Xm06n0xWSyyLSbLfb+VqtdjgSeJTNZlUmk9kmuQrg2h/EWOCR1no+CIJ9AFMisjsWaK3ngiD4BmBGRH60Wq2lkSAp3Gg0bhJBb4y9wZ19OPGUeuGvAF71x+iHh8BgGMD3MAw/O+duE296kvBDh0nDXfCccBcYY7YArAHYD8Nw5fHMQ8/bGLPhv6aILFtr757801rrTyR/ThL2m90DkinTrTO74aMAAAAASUVORK5CYII=) no-repeat right center;
	background-size: 12rpx auto;
	padding-right: 30rpx;
}
.my .nav .kf .xx{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}
.my .nav .kf .xx image{
	width: 45rpx;
	height: 45rpx;
}
.my .nav .kf .xx view{
	font-size: 28rpx;
	line-height: 100rpx;
	color: #333;
	margin-left: 25rpx;
}
.my .nav .kf text{
	font-size: 28rpx;
	line-height: 100rpx;
	color: #3F76F8;
}
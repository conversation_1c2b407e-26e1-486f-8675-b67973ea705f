// packageB/my/order/index.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		states: [
			{
				state: '',
				title: '全部',
			},
			{
				state: '1',
				title: '使用中',
			},
			{
				state: '2',
				title: '待支付',
			},
			{
				state: '3',
				title: '已完成',
			},
		],// 状态

		type: '',// 选中状态

		list: [],// 订单列表
		loading: false,//是否加载中
		loadend: false,//是否加载完毕
		loadTitle: '加载更多',//提示语
		page: 1,
		page_size: 15,
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.setData({
			type: options.type
		})
		this.getList()
	},

	// 切换
	getState(e) {
		this.setData({
			type: e.currentTarget.dataset.type,
			list: [],
			loading: false,//是否加载中
			loadend: false,//是否加载完毕
			page: 1,
		})
		this.getList();
	},

	// 订单列表
	getList() {
		var that = this;
		if (that.data.loadend) return;
		if (that.data.loading) return;
		let params = {
			status: that.data.type,
			page: that.data.page,
			page_size: that.data.page_size,
		}
		app.post('Order/getMyOrder', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				if (that.data.page == 1) {
					that.setData({
						list: [],
					});
				}
				// 数据赋值
				var list = data.data || [];
				// 合并数组
				that.data.list = app.SplitArray(list, that.data.list);
				var loadend = that.data.list.length >= data.count;
				that.setData({
					list: that.data.list,
					loadend: loadend,
					loading: false,
					loadTitle: loadend ? "到底啦..." : '加载更多',
					page: that.data.page + 1,
				});
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 短时免单-套餐抵扣
	getPay(e) {
		let that = this
		let pay_status = e.currentTarget.dataset.pay_status
		let text = ''
		let params = {
			ordercode: e.currentTarget.dataset.ordercode
		}
		app.post('Order/orderFreeSheet', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				if (pay_status == 3) {
					text = '车锁费用已免单~'
				} else {
					text = '车锁费用已用套餐抵扣~'
				}
				wx.showModal({
					title: '提示',
					content: text,
					showCancel: false,
					confirmText: '我知道了',
					success(res) {
						if (res.confirm) {
							that.setData({
								list: [],
								loading: false,//是否加载中
								loadend: false,//是否加载完毕
								page: 1,
							});
							that.getList();
						}
					}
				})
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	// 去支付
	goPay(e){
		wx.navigateTo({
			url: '/packageB/pay/index?money=' + e.currentTarget.dataset.money + '&ordercode=' + e.currentTarget.dataset.ordercode,
		})
	},

	// 订单详情
	goDetails(e) {
		wx.navigateTo({
			url: 'details?id=' + e.currentTarget.dataset.id,
		})
	},


	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {

	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {
		let that = this
		that.setData({
			list: [],
			stopPull: true,

			loading: false,//是否加载中
			loadend: false,//是否加载完毕
			page: 1,
		});
		that.getList();
		wx.stopPullDownRefresh();
	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {
		this.getList();
	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})
// 小程序富文本插件 https://github.com/jin-yufeng/Parser
"use strict";function _classCallCheck(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}var _createClass=function(){function t(t,i){for(var s=0;s<i.length;s++){var e=i[s];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(t,e.key,e)}}return function(i,s,e){return s&&t(i.prototype,s),e&&t(i,e),i}}(),config=require("./config.js"),blankChar=config.blankChar,CssHandler=require("./CssHandler.js"),emoji;try{emoji=require("./emoji.js")}catch(t){}var MpHtmlParser=function(){function t(i){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};_classCallCheck(this,t),this.CssHandler=new CssHandler(s.tagStyle),this.data=i,this.DOM=[],this._attrName="",this._attrValue="",this._attrs={},this._domain=s.domain,this._i=0,this._protocol=this._domain&&this._domain.includes("://")?this._domain.split("://")[0]:"http",this._sectionStart=0,this._state=this.Text,this._STACK=[],this._tagName="",this._audioNum=0,this._imgNum=0,this._videoNum=0,this._useAnchor=s.useAnchor,this._whiteSpace=!1}return _createClass(t,[{key:"parse",value:function(){emoji&&(this.data=emoji.parseEmoji(this.data)),config.highlight&&(this.data=this.data.replace(/<[pP][rR][eE]([\s\S]*?)>([\s\S]+?)<\/[pP][rR][eE][\s\S]*?>/g,function(t,i,s){return"<pre"+i+">"+config.highlight(s,i)+"</pre>"})),this.data=this.CssHandler.getStyle(this.data);for(var t=this.data.length;this._i<t;this._i++)this._state(this.data[this._i]);for(this._state==this.Text&&this.setText();this._STACK.length;)this.popNode(this._STACK.pop());return this.DOM.length&&(this.DOM[0].PoweredBy="Parser"),this.DOM}},{key:"setAttr",value:function(){for(config.trustAttrs[this._attrName]&&("src"==this._attrName&&"/"==this._attrValue[0]&&("/"==this._attrValue[1]?this._attrValue=this._protocol+":"+this._attrValue:this._domain&&(this._attrValue=this._domain+this._attrValue)),this._attrs[this._attrName]=this._attrValue?this._attrValue:"src"==this._attrName?"":"T"),this._attrValue="";blankChar[this.data[this._i]];)this._i++;this.checkClose()?this.setNode():this._state=this.AttrName}},{key:"setText",value:function(){var t=this.getSelection();if(t){if(!this._whiteSpace){for(var i,s=[],e=t.length,a=!1;i=t[--e];)(!blankChar[i]&&(a=!0)||!blankChar[s[0]]&&(i=" "))&&s.unshift(i);if(!a)return;t=s.join("")}for(var h,r,n,e=t.indexOf("&");-1!=e&&-1!=(h=t.indexOf(";",e+2));)"#"==t[e+1]?(r=parseInt(("x"==t[e+2]?"0":"")+t.substring(e+2,h)),isNaN(r)||(t=t.substring(0,e)+String.fromCharCode(r)+t.substring(h+1))):(r=t.substring(e+1,h),"nbsp"==r?t=t.substring(0,e)+" "+t.substring(h+1):"lt"!=r&&"gt"!=r&&"amp"!=r&&"ensp"!=r&&"emsp"!=r&&(n=!0)),e=t.indexOf("&",e+2);var l=this._STACK.length?this._STACK[this._STACK.length-1].children:this.DOM;l.length&&"text"==l[l.length-1].type?(l[l.length-1].text+=t,n&&(l[l.length-1].decode=!0)):l.push({type:"text",text:t,decode:n})}}},{key:"setNode",value:function(){var t=this._STACK.length?this._STACK[this._STACK.length-1].children:this.DOM,i={name:this._tagName.toLowerCase(),attrs:this._attrs};if(config.LabelHandler(i,this),this._attrs={},!config.selfClosingTags[i.name]){if(config.ignoreTags[i.name]){for(var s=this._i;this._i<this.data.length;){for(-1==(this._i=this.data.indexOf("</",this._i+1))&&(this._i=this.data.length),this._i+=2,this._sectionStart=this._i;!blankChar[this.data[this._i]]&&">"!=this.data[this._i]&&"/"!=this.data[this._i];)this._i++;if(this.data.substring(this._sectionStart,this._i).toLowerCase()==i.name){if(this._i=this.data.indexOf(">",this._i),-1==this._i?this._i=this.data.length:this._sectionStart=this._i+1,this._state=this.Text,"svg"==i.name){var e=this.data.substring(s,this._i+1);for(i.attrs.xmlns||(e=' xmlns="http://www.w3.org/2000/svg"'+e),this._i=s;"<"!=this.data[s];)s--;e=this.data.substring(s,this._i)+e,this._i=this._sectionStart-1,i.name="img",i.attrs={src:"data:image/svg+xml;utf8,"+e.replace(/#/g,"%23"),ignore:"T"},t.push(i)}break}}return}this._STACK.push(i),i.children=[]}"/"==this.data[this._i]&&this._i++,this._sectionStart=this._i+1,this._state=this.Text,config.ignoreTags[i.name]||(("pre"==i.name||i.attrs.style&&i.attrs.style.includes("white-space")&&i.attrs.style.includes("pre"))&&(this._whiteSpace=!0,i.pre=!0),t.push(i))}},{key:"popNode",value:function(t){if(config.blockTags[t.name]?t.name="div":config.trustTags[t.name]||(t.name="span"),t.pre){this._whiteSpace=!1,t.pre=void 0;for(var i=this._STACK.length;i--;)this._STACK[i].pre&&(this._whiteSpace=!0)}if(t.c)if("ul"==t.name){for(var s=1,i=this._STACK.length;i--;)"ul"==this._STACK[i].name&&s++;if(1!=s)for(i=t.children.length;i--;)t.children[i].floor=s}else if("ol"==t.name)for(var e,i=0,a=1;e=t.children[i++];)"li"==e.name&&(e.type="ol",e.num=function(t,i){if("a"==i)return String.fromCharCode(97+(t-1)%26);if("A"==i)return String.fromCharCode(65+(t-1)%26);if("i"==i||"I"==i){t=(t-1)%99+1;var s=["I","II","III","IV","V","VI","VII","VIII","IX"],e=["X","XX","XXX","XL","L","LX","LXX","LXXX","XC"],a=(e[Math.floor(t/10)-1]||"")+(s[t%10-1]||"");return"i"==i?a.toLowerCase():a}return t}(a++,t.attrs.type)+".");if("table"==t.name){if(t.attrs.border&&(t.attrs.style="border:"+t.attrs.border+"px solid gray;"+(t.attrs.style||"")),t.attrs.hasOwnProperty("cellspacing")&&(t.attrs.style="border-spacing:"+t.attrs.cellspacing+"px;"+(t.attrs.style||"")),t.attrs.border||t.attrs.hasOwnProperty("cellpadding"))for(var i=t.children.length;i--;)!function i(s){if("th"==s.name||"td"==s.name)return t.attrs.border&&(s.attrs.style="border:"+t.attrs.border+"px solid gray;"+(s.attrs.style||"")),void(t.attrs.hasOwnProperty("cellpadding")&&(s.attrs.style="padding:"+t.attrs.cellpadding+"px;"+(s.attrs.style||"")));if("text"!=s.type)for(var e=(s.children||[]).length;e--;)i(s.children[e])}(t.children[i])}if(1==t.children.length&&"div"==t.name&&"div"==t.children[0].name){var e=t.children[0].attrs;t.attrs.style=t.attrs.style||"",e.style=e.style||"",t.attrs.style.includes("padding")||t.attrs.style.includes("margin")&&e.style.includes("margin")||t.attrs.style.includes("display")||e.style.includes("display")||t.attrs.id||t.attrs.id||t.attrs.class||e.class?(t.attrs.style||(t.attrs.style=void 0),e.style||(e.style=void 0)):(e.style.includes("padding")&&(e.style="box-sizing:border-box;"+e.style),t.attrs.style=t.attrs.style+";"+e.style,t.attrs.id=(e.id||"")+(t.attrs.id||""),t.attrs.class=(e.class||"")+(t.attrs.class||""),t.children=t.children[0].children)}this.CssHandler.pop&&this.CssHandler.pop(t)}},{key:"checkClose",value:function(){return">"==this.data[this._i]||"/"==this.data[this._i]&&">"==this.data[this._i+1]}},{key:"getSelection",value:function(t){for(var i=this._sectionStart==this._i?"":this.data.substring(this._sectionStart,this._i);t&&(blankChar[this.data[++this._i]]||(this._i--,!1)););return this._sectionStart=this._i+1,i}},{key:"Text",value:function(t){if("<"==t){var i=this.data[this._i+1];i>="a"&&i<="z"||i>="A"&&i<="Z"?(this.setText(),this._state=this.TagName):"/"==i?(this.setText(),this._i++,i=this.data[this._i+1],i>="a"&&i<="z"||i>="A"&&i<="Z"?(this._sectionStart=this._i+1,this._state=this.EndTag):this._state=this.Comment):"!"==i&&(this.setText(),this._state=this.Comment)}}},{key:"Comment",value:function(){if("--"==this.data.substring(this._i+1,this._i+3)||"[CDATA["==this.data.substring(this._i+1,this._i+7)){if(this._i=this.data.indexOf("--\x3e",this._i+1),-1==this._i)return this._i=this.data.length;this._i=this._i+2}else-1==(this._i=this.data.indexOf(">",this._i+1))&&(this._i=this.data.length);this._sectionStart=this._i+1,this._state=this.Text}},{key:"TagName",value:function(t){blankChar[t]?(this._tagName=this.getSelection(!0),this.checkClose()?this.setNode():this._state=this.AttrName):this.checkClose()&&(this._tagName=this.getSelection(),this.setNode())}},{key:"AttrName",value:function(t){if(blankChar[t])if(this._attrName=this.getSelection(!0).toLowerCase(),"="==this.data[this._i]){for(;blankChar[this.data[++this._i]];);this._sectionStart=this._i--,this._state=this.AttrValue}else this.setAttr();else if("="==t){for(this._attrName=this.getSelection().toLowerCase();blankChar[this.data[++this._i]];);this._sectionStart=this._i--,this._state=this.AttrValue}else this.checkClose()&&(this._attrName=this.getSelection().toLowerCase(),this.setAttr())}},{key:"AttrValue",value:function(t){if('"'==t||"'"==t){if(this._sectionStart++,-1==(this._i=this.data.indexOf(t,this._i+1)))return this._i=this.data.length}else for(;!blankChar[this.data[this._i]]&&">"!=this.data[this._i];this._i++);for(this._attrValue=this.getSelection();this._attrValue.includes("&quot;");)this._attrValue=this._attrValue.replace("&quot;","");this.setAttr()}},{key:"EndTag",value:function(t){if(blankChar[t]||">"==t||"/"==t){for(var i=this.getSelection().toLowerCase(),s=!1,e=this._STACK.length;e--;)if(this._STACK[e].name==i){s=!0;break}if(s)for(var a;s;)a=this._STACK.pop(),a.name==i&&(s=!1),this.popNode(a);else if("p"==i||"br"==i){var h=this._STACK.length?this._STACK[this._STACK.length-1].children:this.DOM;h.push({name:i,attrs:{}})}this._i=this.data.indexOf(">",this._i),-1==this._i?this._i=this.data.length:this._state=this.Text}}}]),t}();module.exports=function(t,i){return new MpHtmlParser(t,i).parse()};